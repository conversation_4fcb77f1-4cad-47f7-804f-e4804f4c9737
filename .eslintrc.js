module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
    jest: true
  },
  extends: [
    'eslint:recommended',
    'prettier'
  ],
  plugins: [
    'jest'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  rules: {
    // Code Quality
    'no-console': 'warn',
    'no-debugger': 'warn',
    'no-unused-vars': ['error', { 
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_'
    }],
    'no-undef': 'error',
    'no-unreachable': 'error',
    'no-duplicate-imports': 'error',
    
    // Best Practices
    'eqeqeq': ['error', 'always'],
    'curly': ['error', 'all'],
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-new-func': 'error',
    'no-return-assign': 'error',
    'no-sequences': 'error',
    'no-throw-literal': 'error',
    'prefer-const': 'error',
    'no-var': 'error',
    
    // Style (handled by Pretti<PERSON>, but some logical rules)
    'camelcase': ['error', { 
      properties: 'never',
      ignoreDestructuring: true,
      allow: ['^UNSAFE_']
    }],
    'new-cap': ['error', { 
      newIsCap: true,
      capIsNew: false
    }],
    
    // Performance considerations for game development
    'no-loop-func': 'error',
    'no-new-object': 'error',
    'no-new-wrappers': 'error',
    
    // Jest specific rules
    'jest/no-disabled-tests': 'warn',
    'jest/no-focused-tests': 'error',
    'jest/no-identical-title': 'error',
    'jest/prefer-to-have-length': 'warn',
    'jest/valid-expect': 'error'
  },
  overrides: [
    {
      files: ['**/*.test.js', '**/*.spec.js'],
      env: {
        jest: true
      },
      rules: {
        'no-console': 'off'
      }
    },
    {
      files: ['server/**/*.js'],
      env: {
        node: true,
        browser: false
      }
    },
    {
      files: ['client/**/*.js'],
      env: {
        browser: true,
        node: false
      },
      globals: {
        // Three.js globals
        'THREE': 'readonly',
        // Cannon.js globals
        'CANNON': 'readonly',
        // Game-specific globals
        '__DEV__': 'readonly',
        '__PROD__': 'readonly',
        '__VERSION__': 'readonly'
      }
    }
  ],
  ignorePatterns: [
    'dist/',
    'coverage/',
    'node_modules/',
    '*.min.js'
  ]
};
