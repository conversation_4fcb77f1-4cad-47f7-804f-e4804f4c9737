/**
 * LoadingScreen - Manages the loading screen UI
 * 
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

export class LoadingScreen {
  constructor() {
    this.element = document.getElementById('loading-screen');
    this.progressBar = document.getElementById('loading-progress');
    this.textElement = document.getElementById('loading-text');
  }

  /**
   * Show loading screen
   */
  show() {
    this.element.classList.remove('hidden');
  }

  /**
   * Hide loading screen
   */
  hide() {
    this.element.classList.add('hidden');
  }

  /**
   * Update progress (0-1)
   */
  updateProgress(progress) {
    const percentage = Math.round(progress * 100);
    this.progressBar.style.width = `${percentage}%`;
  }

  /**
   * Update loading text
   */
  updateText(text) {
    this.textElement.textContent = text;
  }

  /**
   * Show error message
   */
  showError(message) {
    this.textElement.textContent = `Error: ${message}`;
    this.textElement.style.color = '#ff0066';
  }
}
