/**
 * Server-side ErrorHandler utility
 * 
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

import { Logger } from './Logger.js';

const logger = new Logger('ErrorHandler');

export class ErrorHandler {
  static handleError(error, context = 'Unknown') {
    logger.error(`[${context}]`, error);
    
    // In development, log stack trace
    if (process.env.NODE_ENV === 'development') {
      console.error('Stack trace:', error.stack);
    }
    
    // TODO: Send error reports to monitoring service in production
  }
}
