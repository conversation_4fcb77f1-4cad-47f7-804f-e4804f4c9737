/**
 * EnemyAI - Handles enemy behavior and AI for NEON SHARD
 * 
 * Task 1.8: Create simple enemy entities with basic AI. Implement pathfinding, 
 * line-of-sight detection, and basic combat behavior.
 * 
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

import * as THREE from 'three';
import { EventEmitter } from '../utils/EventEmitter.js';
import { Logger } from '../utils/Logger.js';

const logger = new Logger('EnemyAI');

/**
 * Enemy states for AI behavior
 */
const EnemyState = {
  IDLE: 'idle',
  PATROL: 'patrol',
  CHASE: 'chase',
  ATTACK: 'attack',
  DEAD: 'dead'
};

/**
 * Individual Enemy class
 */
class Enemy {
  constructor(id, position, scene, physicsEngine, playerController) {
    this.id = id;
    this.scene = scene;
    this.physicsEngine = physicsEngine;
    this.playerController = playerController;
    
    // Enemy properties
    this.health = 100;
    this.maxHealth = 100;
    this.speed = 3.0;
    this.attackRange = 2.0;
    this.detectionRange = 10.0;
    this.fieldOfView = Math.PI / 3; // 60 degrees
    
    // AI state
    this.state = EnemyState.IDLE;
    this.target = null;
    this.lastKnownPlayerPosition = null;
    this.patrolPoints = [];
    this.currentPatrolIndex = 0;
    this.stateTimer = 0;
    
    // Movement
    this.position = new THREE.Vector3().copy(position);
    this.velocity = new THREE.Vector3();
    this.direction = new THREE.Vector3(0, 0, 1); // Initial facing direction
    this.targetPosition = new THREE.Vector3().copy(position);
    
    // Timing
    this.lastAttackTime = 0;
    this.attackCooldown = 1000; // 1 second
    this.lastPathUpdate = 0;
    this.pathUpdateInterval = 500; // 0.5 seconds
    
    // Create visual representation and physics
    this.createVisual();
    this.createPhysics();
    this.generatePatrolPoints();
    
    // Start with patrol behavior
    this.setState(EnemyState.PATROL);
  }

  /**
   * Create visual representation of the enemy
   */
  createVisual() {
    // Create a simple enemy model (red cube for now)
    const geometry = new THREE.BoxGeometry(0.8, 1.6, 0.8);
    const material = new THREE.MeshBasicMaterial({ 
      color: 0xff4444,
      wireframe: false
    });
    
    this.mesh = new THREE.Mesh(geometry, material);
    this.mesh.position.copy(this.position);
    this.scene.add(this.mesh);
    
    // Add a simple "eye" to show facing direction
    const eyeGeometry = new THREE.SphereGeometry(0.1, 8, 6);
    const eyeMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });
    this.eyeMesh = new THREE.Mesh(eyeGeometry, eyeMaterial);
    this.eyeMesh.position.set(0, 0.3, 0.35);
    this.mesh.add(this.eyeMesh);
    
    // Health bar
    this.createHealthBar();
  }

  /**
   * Create health bar above enemy
   */
  createHealthBar() {
    const barWidth = 1.0;
    const barHeight = 0.1;
    
    // Background
    const bgGeometry = new THREE.PlaneGeometry(barWidth, barHeight);
    const bgMaterial = new THREE.MeshBasicMaterial({ color: 0x333333 });
    this.healthBarBg = new THREE.Mesh(bgGeometry, bgMaterial);
    this.healthBarBg.position.set(0, 1.0, 0);
    this.mesh.add(this.healthBarBg);
    
    // Health bar
    const healthGeometry = new THREE.PlaneGeometry(barWidth, barHeight);
    const healthMaterial = new THREE.MeshBasicMaterial({ color: 0x44ff44 });
    this.healthBar = new THREE.Mesh(healthGeometry, healthMaterial);
    this.healthBar.position.set(0, 1.0, 0.01);
    this.mesh.add(this.healthBar);
  }

  /**
   * Update health bar display
   */
  updateHealthBar() {
    const healthPercent = this.health / this.maxHealth;
    this.healthBar.scale.x = healthPercent;
    
    // Change color based on health
    if (healthPercent > 0.6) {
      this.healthBar.material.color.setHex(0x44ff44); // Green
    } else if (healthPercent > 0.3) {
      this.healthBar.material.color.setHex(0xffff44); // Yellow
    } else {
      this.healthBar.material.color.setHex(0xff4444); // Red
    }
  }

  /**
   * Create physics body for the enemy
   */
  createPhysics() {
    this.body = this.physicsEngine.createRigidBody(this.mesh, {
      mass: 1,
      shape: 'box'
    });
    
    // Prevent enemy from tipping over
    this.body.fixedRotation = true;
    this.body.updateMassProperties();
  }

  /**
   * Generate random patrol points around spawn location
   */
  generatePatrolPoints() {
    const numPoints = 4;
    const radius = 5;
    
    for (let i = 0; i < numPoints; i++) {
      const angle = (i / numPoints) * Math.PI * 2;
      const x = this.position.x + Math.cos(angle) * radius;
      const z = this.position.z + Math.sin(angle) * radius;
      
      this.patrolPoints.push(new THREE.Vector3(x, this.position.y, z));
    }
  }

  /**
   * Check if player is in line of sight
   */
  canSeePlayer() {
    if (!this.playerController) return false;
    
    const playerPos = this.playerController.getPosition();
    const enemyPos = this.position.clone();
    
    // Check distance
    const distance = enemyPos.distanceTo(playerPos);
    if (distance > this.detectionRange) return false;
    
    // Check field of view
    const toPlayer = playerPos.clone().sub(enemyPos).normalize();
    const forward = this.direction.clone().normalize();
    const angle = Math.acos(Math.max(-1, Math.min(1, forward.dot(toPlayer))));
    
    if (angle > this.fieldOfView / 2) return false;
    
    // Raycast to check for obstacles
    const rayStart = new THREE.Vector3(enemyPos.x, enemyPos.y + 0.8, enemyPos.z);
    const rayEnd = new THREE.Vector3(playerPos.x, playerPos.y, playerPos.z);
    
    const raycast = this.physicsEngine.raycast(rayStart, rayEnd);
    
    // If raycast hits something before reaching player, no line of sight
    if (raycast.hasHit && raycast.distance < distance - 0.5) {
      return false;
    }
    
    return true;
  }

  /**
   * Set enemy state
   */
  setState(newState) {
    if (this.state !== newState) {
      logger.debug(`Enemy ${this.id}: ${this.state} -> ${newState}`);
      this.state = newState;
      this.stateTimer = 0;
    }
  }

  /**
   * Take damage
   */
  takeDamage(amount) {
    this.health -= amount;
    this.updateHealthBar();
    
    if (this.health <= 0) {
      this.setState(EnemyState.DEAD);
      logger.info(`Enemy ${this.id} killed`);
    } else {
      // If hit, become aggressive
      if (this.state === EnemyState.PATROL || this.state === EnemyState.IDLE) {
        this.setState(EnemyState.CHASE);
        this.lastKnownPlayerPosition = this.playerController.getPosition().clone();
      }
    }
  }

  /**
   * Move towards target position
   */
  moveTowards(targetPos, deltaTime) {
    const direction = targetPos.clone().sub(this.position).normalize();
    const distance = this.position.distanceTo(targetPos);
    
    if (distance > 0.5) {
      // Update facing direction
      this.direction.copy(direction);
      
      // Apply movement
      const moveVector = direction.multiplyScalar(this.speed * deltaTime);
      
      // Use physics for movement
      if (this.body) {
        this.body.velocity.x = moveVector.x / deltaTime;
        this.body.velocity.z = moveVector.z / deltaTime;
        
        // Update position from physics body
        this.position.copy(this.body.position);
      }
      
      // Rotate mesh to face movement direction
      const angle = Math.atan2(direction.x, direction.z);
      this.mesh.rotation.y = angle;
      
      return false; // Not reached target
    }
    
    return true; // Reached target
  }

  /**
   * Update AI behavior
   */
  update(deltaTime) {
    if (this.state === EnemyState.DEAD) return;
    
    this.stateTimer += deltaTime;
    
    // Always check for player visibility
    const canSeePlayer = this.canSeePlayer();
    
    switch (this.state) {
      case EnemyState.IDLE:
        this.updateIdleState(deltaTime, canSeePlayer);
        break;
      case EnemyState.PATROL:
        this.updatePatrolState(deltaTime, canSeePlayer);
        break;
      case EnemyState.CHASE:
        this.updateChaseState(deltaTime, canSeePlayer);
        break;
      case EnemyState.ATTACK:
        this.updateAttackState(deltaTime, canSeePlayer);
        break;
    }
  }

  /**
   * Update idle state
   */
  updateIdleState(deltaTime, canSeePlayer) {
    if (canSeePlayer) {
      this.setState(EnemyState.CHASE);
      this.lastKnownPlayerPosition = this.playerController.getPosition().clone();
    } else if (this.stateTimer > 2.0) {
      this.setState(EnemyState.PATROL);
    }
  }

  /**
   * Update patrol state
   */
  updatePatrolState(deltaTime, canSeePlayer) {
    if (canSeePlayer) {
      this.setState(EnemyState.CHASE);
      this.lastKnownPlayerPosition = this.playerController.getPosition().clone();
      return;
    }
    
    // Move to current patrol point
    const targetPoint = this.patrolPoints[this.currentPatrolIndex];
    if (this.moveTowards(targetPoint, deltaTime)) {
      // Reached patrol point, move to next
      this.currentPatrolIndex = (this.currentPatrolIndex + 1) % this.patrolPoints.length;
    }
  }

  /**
   * Update chase state
   */
  updateChaseState(deltaTime, canSeePlayer) {
    if (canSeePlayer) {
      this.lastKnownPlayerPosition = this.playerController.getPosition().clone();
    }
    
    if (this.lastKnownPlayerPosition) {
      const distance = this.position.distanceTo(this.lastKnownPlayerPosition);
      
      if (distance <= this.attackRange) {
        this.setState(EnemyState.ATTACK);
      } else {
        this.moveTowards(this.lastKnownPlayerPosition, deltaTime);
      }
      
      // If lost player for too long, return to patrol
      if (!canSeePlayer && this.stateTimer > 5.0) {
        this.setState(EnemyState.PATROL);
        this.lastKnownPlayerPosition = null;
      }
    } else {
      this.setState(EnemyState.PATROL);
    }
  }

  /**
   * Update attack state
   */
  updateAttackState(deltaTime, canSeePlayer) {
    if (!canSeePlayer) {
      this.setState(EnemyState.CHASE);
      return;
    }
    
    const playerPos = this.playerController.getPosition();
    const distance = this.position.distanceTo(playerPos);
    
    if (distance > this.attackRange * 1.5) {
      this.setState(EnemyState.CHASE);
      return;
    }
    
    // Face the player
    const toPlayer = playerPos.clone().sub(this.position).normalize();
    this.direction.copy(toPlayer);
    const angle = Math.atan2(toPlayer.x, toPlayer.z);
    this.mesh.rotation.y = angle;
    
    // Attack if cooldown is ready
    const now = Date.now();
    if (now - this.lastAttackTime > this.attackCooldown) {
      this.attack();
      this.lastAttackTime = now;
    }
  }

  /**
   * Perform attack
   */
  attack() {
    logger.info(`Enemy ${this.id} attacks player!`);
    
    // Simple attack - could damage player or fire projectile
    // For now, just log the attack
    
    // Visual feedback - flash red
    this.mesh.material.color.setHex(0xff8888);
    setTimeout(() => {
      if (this.mesh && this.mesh.material) {
        this.mesh.material.color.setHex(0xff4444);
      }
    }, 200);
  }

  /**
   * Destroy enemy
   */
  destroy() {
    if (this.mesh) {
      this.scene.remove(this.mesh);
      this.mesh.geometry.dispose();
      this.mesh.material.dispose();
    }
    
    if (this.body) {
      this.physicsEngine.removeRigidBody(this.mesh);
    }
  }
}

/**
 * EnemyAI system for managing all enemies
 */
export class EnemyAI extends EventEmitter {
  constructor(scene, physicsEngine, playerController) {
    super();

    this.scene = scene;
    this.physicsEngine = physicsEngine;
    this.playerController = playerController;

    // Enemy management
    this.enemies = new Map();
    this.nextEnemyId = 1;
    this.maxEnemies = 5;

    // Spawn settings
    this.spawnRadius = 15;
    this.spawnCooldown = 3000; // 3 seconds
    this.lastSpawnTime = 0;
  }

  /**
   * Initialize the enemy AI system
   */
  async init() {
    try {
      logger.info('Initializing EnemyAI system...');

      // Spawn initial enemies
      this.spawnInitialEnemies();

      logger.info('EnemyAI system initialized successfully');

    } catch (error) {
      logger.error(`Failed to initialize EnemyAI: ${error.message}`);
      throw error;
    }
  }

  /**
   * Spawn initial set of enemies
   */
  spawnInitialEnemies() {
    const numEnemies = 3;

    for (let i = 0; i < numEnemies; i++) {
      const angle = (i / numEnemies) * Math.PI * 2;
      const distance = 8 + Math.random() * 5; // 8-13 units from center

      const x = Math.cos(angle) * distance;
      const z = Math.sin(angle) * distance;
      const y = 1.0; // Ground level + height

      this.spawnEnemy(new THREE.Vector3(x, y, z));
    }
  }

  /**
   * Spawn an enemy at the specified position
   */
  spawnEnemy(position) {
    if (this.enemies.size >= this.maxEnemies) {
      logger.warn('Maximum enemy count reached');
      return null;
    }

    const enemyId = this.nextEnemyId++;
    const enemy = new Enemy(
      enemyId,
      position,
      this.scene,
      this.physicsEngine,
      this.playerController
    );

    this.enemies.set(enemyId, enemy);

    logger.info(`Enemy ${enemyId} spawned at (${position.x.toFixed(1)}, ${position.y.toFixed(1)}, ${position.z.toFixed(1)})`);

    this.emit('enemySpawned', { enemy, position });

    return enemy;
  }

  /**
   * Remove an enemy
   */
  removeEnemy(enemyId) {
    const enemy = this.enemies.get(enemyId);
    if (enemy) {
      enemy.destroy();
      this.enemies.delete(enemyId);

      logger.info(`Enemy ${enemyId} removed`);
      this.emit('enemyRemoved', { enemyId });
    }
  }

  /**
   * Handle projectile hit on enemy
   */
  handleProjectileHit(enemyId, damage = 25) {
    const enemy = this.enemies.get(enemyId);
    if (enemy && enemy.state !== EnemyState.DEAD) {
      enemy.takeDamage(damage);

      if (enemy.state === EnemyState.DEAD) {
        // Remove dead enemy after a delay
        setTimeout(() => {
          this.removeEnemy(enemyId);
        }, 2000);
      }

      this.emit('enemyHit', { enemyId, damage, health: enemy.health });
    }
  }

  /**
   * Get enemy at position (for collision detection)
   */
  getEnemyAtPosition(position, radius = 1.0) {
    for (const [id, enemy] of this.enemies) {
      if (enemy.state !== EnemyState.DEAD) {
        const distance = enemy.position.distanceTo(position);
        if (distance <= radius) {
          return { id, enemy };
        }
      }
    }
    return null;
  }

  /**
   * Spawn enemies periodically
   */
  updateSpawning(deltaTime) {
    const now = Date.now();

    // Only spawn if below max and cooldown elapsed
    if (this.enemies.size < this.maxEnemies &&
        now - this.lastSpawnTime > this.spawnCooldown) {

      // Find a spawn position away from player
      const playerPos = this.playerController.getPosition();
      let spawnPos = null;
      let attempts = 0;

      while (!spawnPos && attempts < 10) {
        const angle = Math.random() * Math.PI * 2;
        const distance = this.spawnRadius + Math.random() * 5;

        const x = playerPos.x + Math.cos(angle) * distance;
        const z = playerPos.z + Math.sin(angle) * distance;
        const y = 1.0;

        const testPos = new THREE.Vector3(x, y, z);

        // Check if position is clear (simple check)
        if (!this.getEnemyAtPosition(testPos, 2.0)) {
          spawnPos = testPos;
        }

        attempts++;
      }

      if (spawnPos) {
        this.spawnEnemy(spawnPos);
        this.lastSpawnTime = now;
      }
    }
  }

  /**
   * Update all enemies
   */
  update(deltaTime) {
    // Update spawning
    this.updateSpawning(deltaTime);

    // Update all enemies
    for (const [id, enemy] of this.enemies) {
      enemy.update(deltaTime);
    }
  }

  /**
   * Get all enemies
   */
  getEnemies() {
    return Array.from(this.enemies.values());
  }

  /**
   * Get enemy count
   */
  getEnemyCount() {
    return this.enemies.size;
  }

  /**
   * Get alive enemy count
   */
  getAliveEnemyCount() {
    let count = 0;
    for (const enemy of this.enemies.values()) {
      if (enemy.state !== EnemyState.DEAD) {
        count++;
      }
    }
    return count;
  }

  /**
   * Cleanup and shutdown
   */
  shutdown() {
    logger.info('Shutting down EnemyAI system...');

    // Remove all enemies
    for (const [id, enemy] of this.enemies) {
      enemy.destroy();
    }

    this.enemies.clear();

    logger.info('EnemyAI system shutdown complete');
  }
}
