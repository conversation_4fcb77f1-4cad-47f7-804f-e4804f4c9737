/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - Centralized error handling
 * 
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

import { Logger } from './Logger.js';

const logger = new Logger('ErrorHandler');

export class ErrorHandler {
  /**
   * Handle application errors
   */
  static handleError(error, context = 'Unknown') {
    logger.error(`[${context}]`, error);
    
    // In development, show detailed error info
    if (__DEV__) {
      console.error('Detailed error:', error);
    }
    
    // TODO: Send error reports to monitoring service in production
  }
}
