/**
 * Server-side Logger utility
 * 
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

export class Logger {
  constructor(context = 'Server') {
    this.context = context;
  }

  info(message, ...args) {
    console.log(`[${new Date().toISOString()}] [${this.context}] INFO: ${message}`, ...args);
  }

  warn(message, ...args) {
    console.warn(`[${new Date().toISOString()}] [${this.context}] WARN: ${message}`, ...args);
  }

  error(message, ...args) {
    console.error(`[${new Date().toISOString()}] [${this.context}] ERROR: ${message}`, ...args);
  }

  debug(message, ...args) {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[${new Date().toISOString()}] [${this.context}] DEBUG: ${message}`, ...args);
    }
  }
}
