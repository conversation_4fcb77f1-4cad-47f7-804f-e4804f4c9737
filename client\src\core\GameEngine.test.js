/**
 * GameEngine unit tests
 * 
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

import { GameEngine } from './GameEngine.js';

describe('GameEngine', () => {
  let gameEngine;

  beforeEach(() => {
    // Mock canvas element
    document.body.innerHTML = '<canvas id="game-canvas"></canvas><div id="hud-container"></div>';
    gameEngine = new GameEngine();
  });

  afterEach(() => {
    if (gameEngine) {
      gameEngine.shutdown();
    }
  });

  test('should initialize without errors', async () => {
    expect(gameEngine).toBeDefined();
    expect(gameEngine.isRunning).toBe(false);
  });

  test('should emit initialized event after init', async () => {
    const initSpy = jest.fn();
    gameEngine.on('initialized', initSpy);
    
    await gameEngine.init();
    
    expect(initSpy).toHaveBeenCalled();
  });

  test('should start game loop', () => {
    gameEngine.start();
    expect(gameEngine.isRunning).toBe(true);
  });

  test('should pause and resume', () => {
    gameEngine.start();
    gameEngine.pause();
    expect(gameEngine.isPaused).toBe(true);
    
    gameEngine.resume();
    expect(gameEngine.isPaused).toBe(false);
  });
});
