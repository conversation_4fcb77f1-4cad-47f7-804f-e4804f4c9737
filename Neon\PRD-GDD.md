Product Requirements Document (PRD): Project NEON SHARD
Version: 1.0
Date: October 26, 2023
Author: Expert Game Developer AI
1. Vision Statement
Project: NEON SHARD is a fast-paced, competitive arena first-person shooter built for the browser. It is a love letter to the classic arena shooters of the late '90s, reimagined with a stylized, low-poly "cyber-retro" aesthetic and powered by modern web technologies. The core experience revolves around fluid, acrobatic movement, high-impact combat with a distinct time-to-kill, and tactical map control. It's designed for players seeking a quick, skill-based competitive fix without the friction of a large download or installation.
2. Core Pillars
These are the four foundational principles that will guide every design and development decision.
Fluid, Acrobatic Movement: The player should feel fast, agile, and in complete control. Movement is not just for traversal; it is a primary tool for offense and defense.
Tactile, High-Impact Combat: Every shot, hit, and elimination must feel satisfying. This is achieved through a synergy of crisp sound design, punchy visual effects, and responsive weapon mechanics.
Stylized, Performant Visuals: We will embrace a unique, low-poly art style with emissive materials and a stark, neon-infused color palette. This ensures a memorable aesthetic that is highly performant, allowing us to hit a stable 60 FPS on a wide range of hardware.
Accessible, Competitive Multiplayer: The game should be easy to learn but have a high skill ceiling. The barrier to entry is low (just a URL), but success is determined by precision, tactical positioning, and mastery of movement.
3. Gameplay & Mechanics
3.1. Core Loop
The player's minute-to-minute experience is a cycle of:
SPAWN -> LOOT/POSITION (secure a better weapon or advantageous position) -> ENGAGE (utilize movement and aim to eliminate opponents) -> CONTROL (use eliminations to secure map objectives) -> REPEAT.
3.2. Player Movement
Base Speed: 4.0 units/second.
Sprint: 6.0 units/second (SHIFT key). Cannot fire while sprinting.
Jump: Standard vertical jump.
Dash: A short, quick horizontal burst of speed (SPACE double-tap). 2-second cooldown. Provides i-frames for 150ms. This is the core acrobatic mechanic for dodging and aggressive pushes.
Crouch: Reduces player height and slows movement to 2.0 units/second. Increases accuracy slightly.
3.3. Combat System
Health/Armor: 100 Health. Armor can be picked up (50 points) and absorbs 50% of incoming damage until depleted.
Hit Detection: Server-authoritative raycasting. Client-side effects are predictive.
Damage Modifiers: Headshots (2.0x damage), Body (1.0x damage), Limbs (0.75x damage).
Kill Cam: On death, the player views a 5-second replay from their killer's perspective, starting 4 seconds before the kill.
3.4. Weapon Arsenal
Each weapon has a specific role. Players spawn with the Shard Pistol. Other weapons are pickups on the map with fixed spawn timers.
Shard Pistol (Sidearm):
Role: Reliable fallback weapon.
Damage: 20 (Body) / 40 (Head)
Fire Rate: Semi-automatic, 300 RPM.
Magazine: 12 rounds.
Pulse Rifle (Assault):
Role: Versatile mid-range workhorse.
Damage: 25 (Body) / 50 (Head)
Fire Rate: Full-auto, 500 RPM.
Recoil: Moderate, controllable vertical kick.
Magazine: 30 rounds.
Scattergun (Close Quarters):
Role: High-risk, high-reward CQC powerhouse.
Damage: 15 per pellet, 8 pellets per shot.
Fire Rate: Pump-action, 70 RPM.
Magazine: 5 shells.
Rail Laser (Sniper):
Role: Long-range precision elimination.
Damage: 110 (Body) / 220 (Head). A single headshot is lethal.
Fire Rate: Bolt-action, 45 RPM.
Magazine: 4 rounds.
Special: Leaves a bright, visible tracer line for 1 second, revealing the sniper's position.
Volt SMG (Flanker):
Role: High-speed, close-to-mid range harasser.
Damage: 15 (Body) / 30 (Head)
Fire Rate: Full-auto, 900 RPM.
Recoil: High horizontal spread, difficult to control at range.
Magazine: 40 rounds.
3.5. Game Modes (Launch)
Team Deathmatch (4v4): First team to 50 eliminations wins. 10-minute match timer.
Control (4v4): Three control points (A, B, C) on the map. Teams score points for each second they hold a point. First team to 250 points wins.
3.6. AI Opponents ("Echoes")
AI bots, called "Echoes," will be available in a dedicated "Training Ground" mode.
They will use the same NavMesh as players.
Profiles:
Vanguard (Aggressive): Pushes objectives and seeks direct combat.
Sentinel (Defensive): Prefers to hold power positions and guard control points.
Stalker (Flanking): Takes wider routes to attack from unexpected angles.
4. Art & Audio Direction
Visual Style: "Cyber-Retro." Low-poly models with clean, hard edges. Materials will be primarily flat-shaded, with heavy use of emissive textures for weapon details, UI elements, and environmental highlights. No photorealistic textures.
Color Palette: A foundation of dark blues, deep purples, and black. Accents are vibrant neon teal, magenta, and electric yellow. Each team has a color (Blue vs. Orange).
UI/UX: Minimalist and diegetic where possible. The HUD will be clean, vector-like graphics using the accent color palette. Key elements:
Health/Armor Bar (bottom center)
Ammo Counter (bottom right)
Cooldown Timers (above health bar)
Minimap (top left)
Killfeed (top right)
Audio:
Soundtrack: Driving, synth-wave inspired tracks for menus and in-game.
SFX: Digital, punchy, and clear. Weapon sounds will be distinct and instantly recognizable. Hits will have a satisfying "crunch" or "shatter" sound. Footsteps will be clear and directional.
5. Technical Specification
Engine: Custom engine built on Three.js (WebGL 2.0).
Physics: Cannon-es (a maintained fork of Cannon.js) for collision detection and rigid body simulation.
Networking: WebSockets via Socket.IO. It offers excellent fallback support and a simple API, ideal for rapid development. The <150ms latency goal is achievable with a well-designed netcode and properly located servers.
Server Architecture: Authoritative server model running on Node.js. The server will handle all game logic, physics, and hit detection. Clients send inputs and receive game state snapshots, performing client-side prediction and interpolation to smooth gameplay.
Asset Pipeline:
Models: GLTF. Low-poly (<2k triangles per weapon, <500 per prop).
Audio: WAV for short, critical SFX (gunshots, hits). MP3 for music and longer ambient sounds.
Initial Map Count: One highly polished map at launch: "The Kiln". A symmetrical arena with a mix of tight corridors, open spaces, and verticality to support all weapon types and movement styles.
6. Testing & QA Plan
Quality is paramount. Our testing strategy is multi-layered.
Unit Tests (Jest): Every logic-heavy module (DamageSystem.js, AIController.js, etc.) will have comprehensive unit tests covering its public API and edge cases. Run on every commit.
Integration Tests: Test the interaction between modules (e.g., does the PlayerController correctly call the WeaponSystem on fire input?).
End-to-End (E2E) Tests (Playwright): Automated tests that launch a browser, join a game, and perform basic actions like moving, shooting, and verifying UI updates. This catches high-level regressions.
Performance Testing:
A standardized 60-second benchmark path will be run on the test map.
Metrics (FPS, Frame Time, Memory Usage) will be recorded on target hardware (defined as a 4-core CPU, GTX 1060-level GPU).
Performance must not regress beyond a 5% threshold between builds.
Manual Playtesting Checklist: A mandatory checklist to be completed before merging major features, covering:
Controls responsiveness (mouse, keyboard, dash).
Visual fidelity (all effects rendering correctly).
Audio cues (all sounds playing as expected).
Game feel (does the recoil feel right? is movement fluid?).
Granular Implementation Document: Project NEON SHARD
This document breaks down the PRD into a concrete, sequential build plan. Each task includes a ✅ Verification Step which must be passed before moving to the next.
Phase 1: Core Engine & Player Foundation (MVP)
Goal: A single player can run around a test level and shoot a basic weapon.
[ ] 1.0 Core Game Engine Setup
[ ] 1.1 main.js & index.html: Create the entry point. Initialize a Three.js WebGLRenderer (requesting a WebGL2 context), a Scene, and a PerspectiveCamera.
✅ Verification: A blank browser page with a black canvas is rendered without console errors.
[ ] 1.2 GameEngine.js: Implement the core game loop. Use requestAnimationFrame. Calculate deltaTime for frame-rate independent logic. Create update() and render() methods.
✅ Verification: A simple cube added to the scene rotates at a consistent speed, regardless of the browser's frame rate.
[ ] 1.3 Basic Asset Loader: Create a simple manager to load GLTF models and textures using Three.js loaders. Implement a promise-based system to know when all critical assets are loaded.
✅ Verification: A pre-made GLTF model (e.g., a simple crate) can be loaded and displayed in the scene. A loading screen is visible until the model is ready.
[ ] 1.4 Physics World Setup: Integrate cannon-es. Create a PhysicsEngine class that initializes a CANNON.World. In the game loop, step the physics world forward in time.
✅ Verification: A Three.js cube (visual) is linked to a CANNON.Body (physical). It falls and collides with a static CANNON.Plane ground plane.
[ ] 2.0 Player Systems
[ ] 2.1 First-Person Camera Controls: In PlayerController.js, implement mouse-look. Use the Pointer Lock API on canvas click. Listen to mousemove events to update the camera's rotation. Clamp vertical pitch between -90 and +90 degrees.
✅ Verification: Clicking the canvas locks the mouse. Moving the mouse rotates the camera. The camera cannot flip upside down. Pressing ESC unlocks the mouse.
[ ] 2.2 Player Movement & Physics: Create a player capsule CANNON.Body. In PlayerController.js, listen to keydown/keyup for WASD. On update, apply forces or set velocity on the physics body based on input and camera direction.
✅ Verification: The player can move around the test level using WASD. The movement is relative to the camera's direction.
[ ] 2.3 Player-World Collision: Build a simple test level with walls and ramps using CANNON.Box bodies.
✅ Verification: The player's physics body collides with the level geometry. The player can walk up ramps and is stopped by walls.
[ ] 2.4 Basic Weapon Handling: Create a WeaponSystem.js. On a key press (e.g., '1'), attach a placeholder weapon model (a simple GLTF) to the camera as a child object so it's always visible in the foreground (view model).
✅ Verification: Pressing '1' makes a weapon model appear in the bottom-right of the screen. It stays correctly positioned and oriented as the player moves and looks around.
Phase 2: Combat Mechanics & AI
Goal: The player can fight and be fought by basic AI bots in the test level.
[ ] 3.0 Combat Mechanics
[ ] 3.1 Weapon Base Class & Firing: Create a Weapon.js base class. Implement a fire() method. In PlayerController.js, listen for mousedown to call fire(). The fire() method will perform a CANNON.Raycast from the camera's position.
✅ Verification: Clicking the mouse fires a raycast. Use debug lines to visualize the ray. Console log any physics bodies it hits.
[ ] 3.2 Implement Shard Pistol: Create ShardPistol.js extending Weapon. Define its stats (damage, fire rate, mag size) from the PRD. Load its specific GLTF model.
✅ Verification: The player has the Shard Pistol. It fires semi-automatically. The ammo count in the console decreases.
[ ] 3.3 Damage & Health: Create a DamageSystem.js. Give game objects a health property. When the raycast hits an object with health, inflict damage.
✅ Verification: Firing at a target (e.g., a "dummy" cube with 100 health) reduces its health. After 5 shots, the cube is "destroyed" (removed from the scene).
[ ] 3.4 Visual & Audio Feedback: Create a ParticleSystem.js and AudioManager.js.
Muzzle Flash: On fire(), create a temporary, bright sprite at the weapon's barrel.
Impact Effect: On raycast hit, create a small particle burst at the point of impact.
Sound: On fire(), play the pistol's firing sound using the Web Audio API.
✅ Verification: Firing the pistol creates a muzzle flash, an impact spark on the wall, and plays a gunshot sound. All three are synchronized.
[ ] 4.0 AI Implementation
[ ] 4.1 AI Controller & State Machine: Create AIController.js. Give an AI entity a simple state machine (e.g., IDLE, CHASING). The AI will use a raycast to check for line of sight to the player. If seen, switch to CHASING.
✅ Verification: An AI bot stands still. When the player enters its line of sight, it turns to face the player. When the player hides, it stops.
[ ] 4.2 Pathfinding with NavMesh: Create a simple NavMesh for the test level (can be manually defined planes for now). Implement A* pathfinding in Pathfinding.js. When in CHASING state but no line of sight, the AI requests a path to the player's last known location.
✅ Verification: If the AI loses sight of the player around a corner, it navigates around the corner to the player's last position. Visualize the path with debug lines.
Phase 3: Multiplayer & UI
Goal: Two human players can connect to a server and play a basic Team Deathmatch round with a functional HUD.
[ ] 5.0 Multiplayer Architecture
[ ] 5.1 Basic Node.js Server: Create a server.js file. Set up an Express server and integrate Socket.IO.
✅ Verification: A client can connect to the server via WebSocket. The server logs the connection, and the client logs a "connected" message.
[ ] 5.2 Player State Synchronization: When a client connects, spawn a player on the server. The server game loop should tick the physics and game state. At a set interval (e.g., 20 times/sec), broadcast the state of all players (position, rotation) to all clients.
✅ Verification: Two browser windows open. When one player moves, the other player sees them move in their own window with acceptable delay.
[ ] 5.3 Client-Side Interpolation: On the client, don't just teleport remote players to the new positions. Smoothly interpolate (lerp) them from their last position to the new one over time. This hides network jitter.
✅ Verification: The movement of the other player is now smooth, not jerky.
[ ] 5.4 Authoritative Firing: Move the firing logic to the server. The client sends a "fire" input command. The server performs the raycast and damage calculation, then broadcasts the result (e.g., "Player X hit Player Y").
✅ Verification: Player 1 shoots Player 2. Player 2's health (managed by the server) decreases, and this is reflected on both clients' UIs. The hit feels responsive.
[ ] 6.0 UI/UX Systems
[ ] 6.1 Implement HUD: Create HUD.js which manages DOM elements overlaid on the canvas. Display Health and Ammo. These values should be driven by events from the PlayerController and WeaponSystem.
✅ Verification: A health bar and ammo counter are visible. When the player takes damage (from a test script) or fires their weapon, the HUD updates instantly.
(Subsequent phases would flesh out all weapons, game modes, the full UI, and the asset library based on this solid foundation.)