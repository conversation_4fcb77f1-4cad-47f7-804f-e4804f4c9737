# Agent Development Protocol: Project NEON SHARD

## 1. Core Principles

1.  **The PRD is Law:** All implementation must directly adhere to the specifications outlined in the PRD. Any ambiguity must be clarified before implementation.
2.  **One Task at a Time:** Focus exclusively on the current sub-task from the Granular Implementation Document. Do not implement features from future tasks.
3.  **Test Everything, Always:** No code is complete until it is tested. This includes automated unit tests and manual verification.
4.  **Performance is a Feature:** Be mindful of performance at all times. Avoid creating excessive objects in the game loop. Profile and optimize when necessary.
5.  **Clean Code is Required:** Write clear, self-documenting code. Adhere to the specified coding standards.

## 2. Development Workflow

You MUST follow this sequence for every sub-task assigned:

1.  **Acknowledge Task:** State the sub-task you are beginning to work on (e.g., "Working on Task 2.2: Player Movement & Physics").
2.  **Implement Code:** Write the code required to complete ONLY the specified sub-task.
3.  **Write/Update Unit Tests:** Create or modify Jest tests in the corresponding `.test.js` file to cover the new logic. All tests must pass.
4.  **Perform Manual Verification:** Execute the exact steps described in the `✅ Verification` section for the task.
5.  **Submit for Review:**
    *   Provide the complete, changed code files.
    *   State that all unit tests have passed.
    *   Confirm successful completion of the manual verification step by describing the outcome (e.g., "Verification successful: Player collides with walls and does not pass through.").
6.  **Await Approval:** Do not proceed to the next task until the current one has been approved.

## 3. Coding Standards

*   **Language:** JavaScript (ES6+).
*   **Linter:** ESLint with the `eslint:recommended` configuration. All code must be free of linting errors.
*   **Naming Conventions:**
    *   `PascalCase` for classes (`PlayerController`, `GameEngine`).
    *   `camelCase` for functions, methods, and variables (`getPlayerHealth`, `moveSpeed`).
    *   `UPPER_SNAKE_CASE` for constants (`MAX_AMMO`, `PLAYER_SPEED`).
*   **Comments:** Use JSDoc-style comments for all classes and public methods to describe their purpose, parameters, and return values.

## 4. Asset Handling

*   **Placeholders:** If a final asset is not available, use a clearly marked placeholder (e.g., a simple colored cube for a model, a generic "beep" for a sound).
*   **Formats:** Strictly use GLTF for models, WAV for critical SFX, and MP3 for music/ambience.
*   **Performance Budgets:**
    *   Weapon Models: < 2,000 triangles.
    *   Textures: Maximum 512x512 for weapons, 1024x1024 for environmental textures. Use power-of-two dimensions.

## 5. Commit & Version Control

*   **Commit Messages:** Follow the Conventional Commits specification.
    *   **Format:** `type(scope): short description`
    *   **Examples:**
        *   `feat(player): implement WASD movement with physics`
        *   `fix(weapon): prevent firing while reloading`
        *   `docs(readme): update setup instructions`
        *   `test(combat): add unit tests for DamageSystem`

## 6. Testing Mandate

*   **Unit Tests:** Required for all modules containing non-trivial logic. Must achieve >80% code coverage for the module.
*   **Manual Verification:** The `✅ Verification` step is **not optional**. It is the final quality gate for a task. Failure to perform this step accurately constitutes a task failure. Double-check control mappings and asset rendering during this step.