/**
 * GameServer - Core server game logic
 * 
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

import { EventEmitter } from '../utils/EventEmitter.js';
import { Logger } from '../utils/Logger.js';

const logger = new Logger('GameServer');

export class GameServer extends EventEmitter {
  constructor(io, config) {
    super();
    this.io = io;
    this.config = config;
    this.players = new Map();
    this.isRunning = false;
    this.tickInterval = null;
  }

  async init() {
    logger.info('Initializing GameServer...');
    this.setupSocketHandlers();
    logger.info('GameServer initialized');
  }

  setupSocketHandlers() {
    this.io.on('connection', (socket) => {
      this.handlePlayerConnect(socket);
    });
  }

  handlePlayerConnect(socket) {
    logger.info(`Player connected: ${socket.id}`);
    
    socket.on('disconnect', () => {
      this.handlePlayerDisconnect(socket.id);
    });
  }

  handlePlayerDisconnect(playerId) {
    logger.info(`Player disconnected: ${playerId}`);
    this.players.delete(playerId);
  }

  start() {
    this.isRunning = true;
    this.startGameLoop();
    logger.info('GameServer started');
  }

  startGameLoop() {
    const tickRate = 1000 / this.config.tickRate;
    this.tickInterval = setInterval(() => {
      this.update();
    }, tickRate);
  }

  update() {
    // TODO: Update game state
  }

  async stop() {
    this.isRunning = false;
    if (this.tickInterval) {
      clearInterval(this.tickInterval);
    }
    logger.info('GameServer stopped');
  }

  getPlayerCount() {
    return this.players.size;
  }

  getStatistics() {
    return {
      playerCount: this.players.size,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage()
    };
  }
}
