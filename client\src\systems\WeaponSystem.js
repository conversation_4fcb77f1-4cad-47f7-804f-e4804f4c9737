/**
 * WeaponSystem - Handles weapon management, firing, and projectiles for NEON SHARD
 *
 * Task 1.7: Create basic weapon system. Implement weapon switching, firing mechanics,
 * and simple projectile physics. Add crosshair and basic weapon HUD.
 *
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

import * as THREE from 'three';
import { EventEmitter } from '../utils/EventEmitter.js';
import { Logger } from '../utils/Logger.js';

const logger = new Logger('WeaponSystem');

/**
 * Weapon class representing individual weapons
 */
class Weapon {
  constructor(config) {
    this.id = config.id;
    this.name = config.name;
    this.type = config.type; // 'projectile', 'hitscan', 'melee'
    this.damage = config.damage || 25;
    this.fireRate = config.fireRate || 600; // rounds per minute
    this.projectileSpeed = config.projectileSpeed || 50; // m/s
    this.maxAmmo = config.maxAmmo || 30;
    this.currentAmmo = config.currentAmmo || this.maxAmmo;
    this.reloadTime = config.reloadTime || 2000; // milliseconds
    this.crosshairSize = config.crosshairSize || 20;
    this.crosshairColor = config.crosshairColor || '#ffffff';

    // Timing
    this.lastFireTime = 0;
    this.isReloading = false;
    this.reloadStartTime = 0;
  }

  canFire() {
    const now = Date.now();
    const fireInterval = 60000 / this.fireRate; // Convert RPM to milliseconds
    return !this.isReloading &&
           this.currentAmmo > 0 &&
           (now - this.lastFireTime) >= fireInterval;
  }

  fire() {
    if (!this.canFire()) return false;

    this.currentAmmo--;
    this.lastFireTime = Date.now();

    logger.debug(`${this.name} fired! Ammo: ${this.currentAmmo}/${this.maxAmmo}`);
    return true;
  }

  startReload() {
    if (this.isReloading || this.currentAmmo === this.maxAmmo) return false;

    this.isReloading = true;
    this.reloadStartTime = Date.now();

    logger.info(`${this.name} reloading... (${this.reloadTime}ms)`);
    return true;
  }

  updateReload() {
    if (!this.isReloading) return false;

    const now = Date.now();
    if (now - this.reloadStartTime >= this.reloadTime) {
      this.currentAmmo = this.maxAmmo;
      this.isReloading = false;
      logger.info(`${this.name} reload complete!`);
      return true; // Reload finished
    }

    return false; // Still reloading
  }

  getReloadProgress() {
    if (!this.isReloading) return 1.0;

    const elapsed = Date.now() - this.reloadStartTime;
    return Math.min(elapsed / this.reloadTime, 1.0);
  }
}

/**
 * Projectile class for physics-based projectiles
 */
class Projectile {
  constructor(position, direction, weapon, physicsEngine, scene) {
    this.weapon = weapon;
    this.physicsEngine = physicsEngine;
    this.scene = scene;
    this.isActive = true;
    this.lifetime = 5000; // 5 seconds
    this.creationTime = Date.now();

    // Create visual representation
    this.createVisual(position);

    // Create physics body
    this.createPhysicsBody(position, direction);
  }

  createVisual(position) {
    // Create a small sphere for the projectile
    const geometry = new THREE.SphereGeometry(0.05, 8, 6);
    const material = new THREE.MeshBasicMaterial({
      color: 0xffff00,
      emissive: 0x444400
    });

    this.mesh = new THREE.Mesh(geometry, material);
    this.mesh.position.copy(position);
    this.scene.add(this.mesh);
  }

  createPhysicsBody(position, direction) {
    // Create physics body
    this.body = this.physicsEngine.createRigidBody(this.mesh, {
      mass: 0.01, // Very light
      shape: 'sphere'
    });

    // Set initial velocity
    const velocity = direction.clone().multiplyScalar(this.weapon.projectileSpeed);
    this.body.velocity.set(velocity.x, velocity.y, velocity.z);

    // Add collision event listener
    this.body.addEventListener('collide', (event) => {
      this.onCollision(event);
    });
  }

  onCollision(event) {
    logger.debug(`Projectile hit: ${event.target.material?.name || 'unknown'}`);
    this.destroy();
  }

  update(deltaTime) {
    // Check lifetime
    if (Date.now() - this.creationTime > this.lifetime) {
      this.destroy();
      return;
    }

    // Physics handles position updates automatically
  }

  destroy() {
    if (!this.isActive) return;

    this.isActive = false;

    // Remove from scene
    if (this.mesh) {
      this.scene.remove(this.mesh);
      this.mesh.geometry.dispose();
      this.mesh.material.dispose();
    }

    // Remove physics body
    if (this.body) {
      this.physicsEngine.removeRigidBody(this.mesh);
    }

    logger.debug('Projectile destroyed');
  }
}

/**
 * WeaponSystem class for managing all weapons and firing
 */
export class WeaponSystem extends EventEmitter {
  constructor(camera, physicsEngine, scene) {
    super();

    this.camera = camera;
    this.physicsEngine = physicsEngine;
    this.scene = scene;

    // Weapon management
    this.weapons = new Map();
    this.currentWeaponId = null;
    this.currentWeapon = null;

    // Projectile management
    this.projectiles = [];

    // UI elements
    this.crosshair = null;
    this.weaponHUD = null;

    // Initialize default weapons
    this.initializeWeapons();
  }

  /**
   * Initialize default weapon set
   */
  initializeWeapons() {
    const weaponConfigs = [
      {
        id: 'pistol',
        name: 'Pistol',
        type: 'projectile',
        damage: 25,
        fireRate: 300, // 300 RPM
        projectileSpeed: 30,
        maxAmmo: 12,
        reloadTime: 1500,
        crosshairSize: 15,
        crosshairColor: '#ffffff'
      },
      {
        id: 'rifle',
        name: 'Assault Rifle',
        type: 'projectile',
        damage: 35,
        fireRate: 600, // 600 RPM
        projectileSpeed: 50,
        maxAmmo: 30,
        reloadTime: 2500,
        crosshairSize: 20,
        crosshairColor: '#ffaa00'
      },
      {
        id: 'shotgun',
        name: 'Shotgun',
        type: 'projectile',
        damage: 80,
        fireRate: 120, // 120 RPM
        projectileSpeed: 25,
        maxAmmo: 8,
        reloadTime: 3000,
        crosshairSize: 30,
        crosshairColor: '#ff4400'
      },
      {
        id: 'sniper',
        name: 'Sniper Rifle',
        type: 'projectile',
        damage: 100,
        fireRate: 60, // 60 RPM
        projectileSpeed: 100,
        maxAmmo: 5,
        reloadTime: 3500,
        crosshairSize: 10,
        crosshairColor: '#00ff00'
      },
      {
        id: 'rocket',
        name: 'Rocket Launcher',
        type: 'projectile',
        damage: 200,
        fireRate: 30, // 30 RPM
        projectileSpeed: 20,
        maxAmmo: 3,
        reloadTime: 4000,
        crosshairSize: 25,
        crosshairColor: '#ff0000'
      }
    ];

    weaponConfigs.forEach(config => {
      const weapon = new Weapon(config);
      this.weapons.set(config.id, weapon);
    });

    // Set default weapon
    this.switchToWeapon('pistol');

    logger.info(`Initialized ${this.weapons.size} weapons`);
  }

  /**
   * Initialize the weapon system
   */
  async init() {
    try {
      logger.info('Initializing WeaponSystem...');

      // Create crosshair
      this.createCrosshair();

      // Create weapon HUD
      this.createWeaponHUD();

      logger.info('WeaponSystem initialized successfully');

    } catch (error) {
      logger.error(`Failed to initialize WeaponSystem: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create crosshair UI element
   */
  createCrosshair() {
    // Create crosshair container
    this.crosshair = document.createElement('div');
    this.crosshair.id = 'crosshair';
    this.crosshair.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      pointer-events: none;
      z-index: 1000;
      font-family: monospace;
      font-weight: bold;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    `;

    document.body.appendChild(this.crosshair);
    this.updateCrosshair();

    logger.debug('Crosshair created');
  }

  /**
   * Update crosshair appearance based on current weapon
   */
  updateCrosshair() {
    if (!this.crosshair || !this.currentWeapon) return;

    const size = this.currentWeapon.crosshairSize;
    const color = this.currentWeapon.crosshairColor;

    this.crosshair.innerHTML = '+';
    this.crosshair.style.fontSize = `${size}px`;
    this.crosshair.style.color = color;
  }

  /**
   * Create weapon HUD element
   */
  createWeaponHUD() {
    // Create HUD container
    this.weaponHUD = document.createElement('div');
    this.weaponHUD.id = 'weapon-hud';
    this.weaponHUD.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-family: monospace;
      font-size: 14px;
      pointer-events: none;
      z-index: 1000;
      min-width: 200px;
    `;

    document.body.appendChild(this.weaponHUD);
    this.updateWeaponHUD();

    logger.debug('Weapon HUD created');
  }

  /**
   * Update weapon HUD display
   */
  updateWeaponHUD() {
    if (!this.weaponHUD || !this.currentWeapon) return;

    const weapon = this.currentWeapon;
    const reloadProgress = weapon.getReloadProgress();

    let html = `
      <div><strong>${weapon.name}</strong></div>
      <div>Ammo: ${weapon.currentAmmo}/${weapon.maxAmmo}</div>
      <div>Damage: ${weapon.damage}</div>
      <div>Fire Rate: ${weapon.fireRate} RPM</div>
    `;

    if (weapon.isReloading) {
      const progressBar = '█'.repeat(Math.floor(reloadProgress * 10)) +
                         '░'.repeat(10 - Math.floor(reloadProgress * 10));
      html += `<div>Reloading: [${progressBar}]</div>`;
    }

    html += `<div style="margin-top: 10px; font-size: 12px;">`;
    html += `1-5: Switch Weapon | R: Reload | LMB: Fire</div>`;

    this.weaponHUD.innerHTML = html;
  }

  /**
   * Switch to a specific weapon
   */
  switchToWeapon(weaponId) {
    const weapon = this.weapons.get(weaponId);
    if (!weapon) {
      logger.warn(`Weapon not found: ${weaponId}`);
      return false;
    }

    this.currentWeaponId = weaponId;
    this.currentWeapon = weapon;

    this.updateCrosshair();
    this.updateWeaponHUD();

    logger.info(`Switched to weapon: ${weapon.name}`);
    this.emit('weaponSwitched', { weapon: weapon });

    return true;
  }

  /**
   * Switch to weapon by number (1-5)
   */
  switchToWeaponNumber(number) {
    const weaponIds = ['pistol', 'rifle', 'shotgun', 'sniper', 'rocket'];
    const weaponId = weaponIds[number - 1];

    if (weaponId) {
      return this.switchToWeapon(weaponId);
    }

    return false;
  }

  /**
   * Fire the current weapon
   */
  fire() {
    if (!this.currentWeapon) return false;

    if (!this.currentWeapon.fire()) {
      // Can't fire (no ammo, reloading, or rate limit)
      return false;
    }

    // Create projectile
    this.createProjectile();

    // Update HUD
    this.updateWeaponHUD();

    // Emit fire event
    this.emit('fired', {
      weapon: this.currentWeapon,
      position: this.camera.position.clone(),
      direction: this.getFireDirection()
    });

    logger.debug(`${this.currentWeapon.name} fired!`);
    return true;
  }

  /**
   * Get firing direction from camera
   */
  getFireDirection() {
    const direction = new THREE.Vector3(0, 0, -1);
    direction.applyQuaternion(this.camera.quaternion);
    return direction.normalize();
  }

  /**
   * Create a projectile
   */
  createProjectile() {
    // Calculate spawn position (slightly in front of camera)
    const spawnOffset = this.getFireDirection().multiplyScalar(0.5);
    const spawnPosition = this.camera.position.clone().add(spawnOffset);

    // Create projectile
    const projectile = new Projectile(
      spawnPosition,
      this.getFireDirection(),
      this.currentWeapon,
      this.physicsEngine,
      this.scene
    );

    this.projectiles.push(projectile);

    logger.debug('Projectile created');
  }

  /**
   * Reload current weapon
   */
  reload() {
    if (!this.currentWeapon) return false;

    if (this.currentWeapon.startReload()) {
      this.updateWeaponHUD();
      this.emit('reloadStarted', { weapon: this.currentWeapon });
      return true;
    }

    return false;
  }

  /**
   * Update weapon system
   */
  update(deltaTime) {
    // Update current weapon reload
    if (this.currentWeapon && this.currentWeapon.updateReload()) {
      this.updateWeaponHUD();
      this.emit('reloadCompleted', { weapon: this.currentWeapon });
    }

    // Update projectiles
    for (let i = this.projectiles.length - 1; i >= 0; i--) {
      const projectile = this.projectiles[i];
      projectile.update(deltaTime);

      // Remove inactive projectiles
      if (!projectile.isActive) {
        this.projectiles.splice(i, 1);
      }
    }
  }

  /**
   * Get current weapon info
   */
  getCurrentWeapon() {
    return this.currentWeapon;
  }

  /**
   * Get all weapons
   */
  getWeapons() {
    return Array.from(this.weapons.values());
  }

  /**
   * Cleanup and shutdown
   */
  shutdown() {
    logger.info('Shutting down WeaponSystem...');

    // Remove UI elements
    if (this.crosshair) {
      document.body.removeChild(this.crosshair);
      this.crosshair = null;
    }

    if (this.weaponHUD) {
      document.body.removeChild(this.weaponHUD);
      this.weaponHUD = null;
    }

    // Destroy all projectiles
    this.projectiles.forEach(projectile => projectile.destroy());
    this.projectiles = [];

    // Clear weapons
    this.weapons.clear();
    this.currentWeapon = null;

    logger.info('WeaponSystem shutdown complete');
  }
}
