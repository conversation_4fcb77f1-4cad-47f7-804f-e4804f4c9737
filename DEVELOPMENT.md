# Development Guide

This guide covers the development workflow and best practices for NEON SHARD.

## 🚀 Getting Started

### Prerequisites

- **Node.js 18+**: Download from [nodejs.org](https://nodejs.org/)
- **npm 9+**: Comes with Node.js
- **Modern Browser**: Chrome, Firefox, Safari, or Edge with WebGL 2.0 support
- **Git**: For version control

### Initial Setup

1. **Clone and install**
   ```bash
   git clone <repository-url>
   cd neon-shard
   npm install
   ```

2. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Start development**
   ```bash
   npm run dev
   ```

   This starts:
   - Client dev server on `http://localhost:3000`
   - Game server on `http://localhost:3001`

## 🏗️ Project Architecture

### Client-Server Split

- **Client** (`client/`): Browser-based game client
- **Server** (`server/`): Node.js authoritative game server
- **Shared** (`shared/`): Code shared between client and server

### Build System

- **Vite**: Fast development and optimized production builds
- **ES Modules**: Modern JavaScript module system
- **Hot Reload**: Instant updates during development

### Code Organization

```
src/
├── core/           # Core engine systems
├── systems/        # Game systems (input, audio, etc.)
├── ui/            # User interface components
├── utils/         # Utility functions
└── shaders/       # GLSL shaders
```

## 🧪 Testing Strategy

### Unit Tests (Jest)

```bash
npm test                # Run all tests
npm run test:watch     # Watch mode
npm run test:coverage  # With coverage
```

### E2E Tests (Playwright)

```bash
npm run test:e2e       # Run browser tests
```

### Manual Testing

Each feature includes verification steps from the PRD. Always complete these before marking a task as done.

## 📝 Development Workflow

### 1. Task Assignment

Follow the Granular Implementation Document in the PRD. Work on one sub-task at a time.

### 2. Implementation

1. **Acknowledge Task**: State which task you're working on
2. **Implement Code**: Write only the code for the current task
3. **Write Tests**: Create/update unit tests
4. **Manual Verification**: Complete the ✅ verification steps
5. **Submit for Review**: Provide code, test results, and verification

### 3. Code Standards

- **ES6+**: Use modern JavaScript features
- **ESLint**: All code must pass linting
- **Prettier**: Consistent formatting
- **JSDoc**: Document all classes and public methods

### 4. Naming Conventions

- **Classes**: `PascalCase` (e.g., `PlayerController`)
- **Functions/Variables**: `camelCase` (e.g., `getPlayerHealth`)
- **Constants**: `UPPER_SNAKE_CASE` (e.g., `MAX_AMMO`)
- **Files**: `PascalCase.js` for classes, `camelCase.js` for utilities

## 🎮 Game Systems

### Core Systems

1. **GameEngine**: Main game loop and system coordination
2. **PhysicsEngine**: Cannon-es integration for collision detection
3. **InputManager**: Keyboard and mouse input handling
4. **PlayerController**: Player movement and camera controls
5. **WeaponSystem**: Weapon mechanics and firing
6. **NetworkManager**: Client-server communication
7. **AudioManager**: 3D positional audio
8. **ParticleSystem**: Visual effects

### System Communication

Systems communicate through events:

```javascript
// Example: Weapon fires, triggers audio and particles
weaponSystem.on('fired', (data) => {
  audioManager.playSound('pistol-fire');
  particleSystem.createMuzzleFlash(data.position);
});
```

## 🔧 Configuration

### Development Settings

- **Hot Reload**: Enabled by default
- **Source Maps**: Generated for debugging
- **Debug Logging**: Verbose logging in development
- **Performance Monitoring**: FPS and memory tracking

### Production Settings

- **Minification**: Code is minified and optimized
- **Asset Optimization**: Images and models are compressed
- **Error Reporting**: Errors are logged to monitoring service
- **Performance Budget**: Enforced size limits

## 📊 Performance Guidelines

### Frame Rate Targets

- **60 FPS**: Stable on target hardware (GTX 1060 level)
- **30 FPS**: Minimum acceptable on lower-end hardware

### Memory Management

- **Avoid allocations in game loop**: Pre-allocate objects
- **Dispose resources**: Clean up Three.js objects
- **Monitor memory usage**: Use browser dev tools

### Network Optimization

- **<150ms latency**: Target for responsive gameplay
- **20Hz tick rate**: Server update frequency
- **Client prediction**: Smooth movement despite latency

## 🐛 Debugging

### Browser Dev Tools

- **Console**: Check for errors and debug logs
- **Performance**: Profile frame rate and memory
- **Network**: Monitor WebSocket traffic
- **Sources**: Set breakpoints in source code

### Debug Features

- **Debug HUD**: Shows FPS, memory, network stats
- **Physics Visualization**: Render collision shapes
- **Network Logging**: Log all network messages

### Common Issues

1. **WebGL Context Lost**: Handle context restoration
2. **Pointer Lock Issues**: Check browser permissions
3. **Audio Context**: Handle user gesture requirements
4. **Memory Leaks**: Dispose Three.js objects properly

## 🚀 Deployment

### Development Deployment

```bash
npm run build          # Build client
npm run build:server   # Prepare server
npm start             # Start production server
```

### Production Deployment

1. **Build Assets**: `npm run build`
2. **Environment Variables**: Set production values
3. **Server Setup**: Configure reverse proxy (nginx)
4. **SSL Certificate**: Enable HTTPS
5. **CDN**: Serve static assets from CDN

## 📚 Resources

### Documentation

- [Three.js Docs](https://threejs.org/docs/)
- [Cannon-es Docs](https://cannon-es.github.io/cannon-es/)
- [Socket.IO Docs](https://socket.io/docs/)
- [Vite Docs](https://vitejs.dev/)

### Tools

- **VS Code**: Recommended editor with extensions
- **Chrome DevTools**: Primary debugging tool
- **Blender**: 3D model creation and editing
- **Audacity**: Audio editing

### Community

- **Discord**: Development team chat
- **GitHub Issues**: Bug reports and feature requests
- **Wiki**: Extended documentation and guides

---

**Happy coding! 🎮**
