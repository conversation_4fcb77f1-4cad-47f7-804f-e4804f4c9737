# NEON SHARD Environment Configuration

# Server Configuration
PORT=3001
HOST=localhost
NODE_ENV=development

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# Game Configuration
MAX_PLAYERS=8
TICK_RATE=20

# Performance Monitoring
ENABLE_METRICS=true

# Database (if needed in future)
# DATABASE_URL=postgresql://user:password@localhost:5432/neonshard

# Redis (for session management if needed)
# REDIS_URL=redis://localhost:6379

# Logging
LOG_LEVEL=debug

# Security (for production)
# JWT_SECRET=your-secret-key
# SESSION_SECRET=your-session-secret

# CDN Configuration (for production)
# ASSET_CDN_URL=https://cdn.example.com

# Analytics (if needed)
# ANALYTICS_API_KEY=your-analytics-key
