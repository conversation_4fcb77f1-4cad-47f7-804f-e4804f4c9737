export default {
  // Test environment
  testEnvironment: 'jsdom',
  
  // Module paths
  roots: ['<rootDir>/client/src', '<rootDir>/server'],
  
  // Test file patterns
  testMatch: [
    '**/__tests__/**/*.js',
    '**/?(*.)+(spec|test).js'
  ],
  
  // Transform files
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  
  // Module name mapping for aliases
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/client/src/$1',
    '^@assets/(.*)$': '<rootDir>/client/assets/$1',
    '^@shaders/(.*)$': '<rootDir>/client/src/shaders/$1',
    '^@utils/(.*)$': '<rootDir>/client/src/utils/$1',
    '^@systems/(.*)$': '<rootDir>/client/src/systems/$1',
    '^@components/(.*)$': '<rootDir>/client/src/components/$1'
  },
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  
  // Coverage configuration
  collectCoverage: false,
  collectCoverageFrom: [
    'client/src/**/*.js',
    'server/**/*.js',
    '!client/src/**/*.test.js',
    '!client/src/**/*.spec.js',
    '!server/**/*.test.js',
    '!server/**/*.spec.js',
    '!**/node_modules/**',
    '!**/vendor/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: [
    'text',
    'lcov',
    'html'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  
  // Mock configurations for browser APIs and Three.js
  moduleNameMapping: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/tests/__mocks__/fileMock.js'
  },
  
  // Global setup
  globals: {
    __DEV__: true,
    __PROD__: false,
    __VERSION__: '1.0.0'
  },
  
  // Test timeout
  testTimeout: 10000,
  
  // Verbose output
  verbose: true,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Restore mocks after each test
  restoreMocks: true
};
