<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="NEON SHARD - Fast-paced competitive arena FPS">
    <meta name="keywords" content="fps, game, multiplayer, browser, arena shooter">
    <meta name="author" content="Neon Shard Development Team">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/assets/fonts/orbitron.woff2" as="font" type="font/woff2" crossorigin>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/icons/favicon.svg">
    <link rel="icon" type="image/png" href="/assets/icons/favicon.png">
    
    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#00ffff">
    
    <!-- Open Graph meta tags -->
    <meta property="og:title" content="NEON SHARD">
    <meta property="og:description" content="Fast-paced competitive arena FPS">
    <meta property="og:type" content="website">
    <meta property="og:image" content="/assets/images/og-image.png">
    
    <title>NEON SHARD</title>
    
    <!-- Critical CSS for loading screen -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Orbitron', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #00ffff;
            overflow: hidden;
            user-select: none;
        }
        
        #loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            transition: opacity 0.5s ease-out;
        }
        
        #loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        .loading-logo {
            font-size: 4rem;
            font-weight: bold;
            text-shadow: 0 0 20px #00ffff;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }
        
        .loading-bar {
            width: 300px;
            height: 4px;
            background: rgba(0, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 1rem;
        }
        
        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #00ffff, #ff00ff);
            width: 0%;
            transition: width 0.3s ease;
            box-shadow: 0 0 10px #00ffff;
        }
        
        .loading-text {
            font-size: 1rem;
            opacity: 0.8;
            animation: fadeInOut 1.5s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        @keyframes fadeInOut {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 0.4; }
        }
        
        #game-canvas {
            display: block;
            width: 100%;
            height: 100%;
            cursor: none;
        }
        
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen">
        <div class="loading-logo">NEON SHARD</div>
        <div class="loading-bar">
            <div class="loading-progress" id="loading-progress"></div>
        </div>
        <div class="loading-text" id="loading-text">Initializing...</div>
    </div>
    
    <!-- Game Canvas -->
    <canvas id="game-canvas" class="hidden"></canvas>
    
    <!-- HUD Container -->
    <div id="hud-container" class="hidden">
        <!-- HUD elements will be dynamically created here -->
    </div>
    
    <!-- Menu Container -->
    <div id="menu-container" class="hidden">
        <!-- Menu elements will be dynamically created here -->
    </div>
    
    <!-- Debug Container (only in development) -->
    <div id="debug-container" class="hidden">
        <!-- Debug info will be displayed here -->
    </div>
    
    <!-- Main Application Script -->
    <script type="module" src="/src/main.js"></script>
</body>
</html>
