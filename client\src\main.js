/**
 * NEON SHARD - Main Entry Point
 *
 * Task 1.2: GameEngine.js - Implement the core game loop. Use requestAnimationFrame.
 * Calculate deltaTime for frame-rate independent logic. Create update() and render() methods.
 *
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

// Test if script is loading
console.log('NEON SHARD: Script loading...');

import * as THREE from 'three';

// Initialize logger for basic console output
const logger = {
  info: (msg) => console.log(`[NEON SHARD] ${msg}`),
  error: (msg) => console.error(`[NEON SHARD] ${msg}`),
  warn: (msg) => console.warn(`[NEON SHARD] ${msg}`)
};

/**
 * GameEngine class for Task 1.2
 * Implements core game loop with requestAnimationFrame and deltaTime calculation
 */
class GameEngine {
  constructor() {
    // Core Three.js components
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.canvas = null;

    // Game loop variables
    this.isRunning = false;
    this.lastTime = 0;
    this.deltaTime = 0;
    this.frameCount = 0;

    // Test cube for verification
    this.testCube = null;
  }

  /**
   * Initialize the game engine
   */
  async init() {
    try {
      logger.info('Initializing GameEngine - Task 1.2...');

      // Check if Three.js is available
      if (typeof THREE === 'undefined') {
        throw new Error('Three.js library not loaded');
      }
      logger.info('Three.js library loaded successfully');

      // Check WebGL 2.0 support
      if (!this.checkWebGLSupport()) {
        throw new Error('WebGL 2.0 is not supported on this device');
      }
      logger.info('WebGL 2.0 support confirmed');

      // Initialize Three.js components
      this.initRenderer();
      this.initScene();
      this.initCamera();
      this.addTestCube();

      logger.info('GameEngine initialized successfully');

    } catch (error) {
      logger.error(`Failed to initialize GameEngine: ${error.message}`);
      console.error('Full error:', error);
      throw error;
    }
  }

  /**
   * Check if WebGL 2.0 is supported
   */
  checkWebGLSupport() {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2');
    return !!gl;
  }

  /**
   * Initialize the Three.js renderer with WebGL 2.0 context
   */
  initRenderer() {
    this.canvas = document.getElementById('game-canvas');

    if (!this.canvas) {
      throw new Error('Canvas element with id "game-canvas" not found');
    }

    this.renderer = new THREE.WebGLRenderer({
      canvas: this.canvas,
      antialias: true,
      alpha: false,
      powerPreference: 'high-performance'
    });

    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

    const gl = this.renderer.getContext();
    if (gl.getParameter(gl.VERSION).includes('WebGL 2.0')) {
      logger.info('WebGL 2.0 context created successfully');
    } else {
      logger.warn('WebGL 2.0 not available, falling back to WebGL 1.0');
    }
  }

  /**
   * Initialize the Three.js scene
   */
  initScene() {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x000000);
  }

  /**
   * Initialize the perspective camera
   */
  initCamera() {
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 0, 5);
  }

  /**
   * Add a test cube to verify frame-rate independent rotation
   */
  addTestCube() {
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial({
      color: 0x00ffff,
      wireframe: true
    });

    this.testCube = new THREE.Mesh(geometry, material);
    this.scene.add(this.testCube);

    logger.info('Test cube added to scene');
  }

  /**
   * Start the game loop
   */
  start() {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.lastTime = performance.now();

    logger.info('Starting game loop...');
    this.gameLoop();
  }

  /**
   * Main game loop using requestAnimationFrame
   */
  gameLoop() {
    if (!this.isRunning) {
      return;
    }

    // Calculate deltaTime for frame-rate independent logic
    const currentTime = performance.now();
    this.deltaTime = (currentTime - this.lastTime) / 1000; // Convert to seconds
    this.lastTime = currentTime;

    // Cap delta time to prevent large jumps
    this.deltaTime = Math.min(this.deltaTime, 1/30);

    // Update game logic
    this.update(this.deltaTime);

    // Render the scene
    this.render();

    // Continue the loop
    requestAnimationFrame(() => this.gameLoop());

    this.frameCount++;
  }

  /**
   * Update method - called every frame with deltaTime
   */
  update(deltaTime) {
    // Rotate the test cube at a consistent speed regardless of frame rate
    if (this.testCube) {
      this.testCube.rotation.x += deltaTime * 1.0; // 1 radian/second
      this.testCube.rotation.y += deltaTime * 0.5; // 0.5 radian/second
    }
  }

  /**
   * Render method - renders the scene
   */
  render() {
    this.renderer.render(this.scene, this.camera);
  }

  /**
   * Handle window resize
   */
  handleResize() {
    if (this.renderer && this.camera) {
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
  }

  /**
   * Show the canvas and hide loading screen
   */
  showCanvas() {
    const loadingScreen = document.getElementById('loading-screen');

    if (loadingScreen) {
      loadingScreen.classList.add('hidden');
    }

    this.canvas.classList.remove('hidden');
  }

  /**
   * Show error message
   */
  showError(message) {
    const loadingText = document.getElementById('loading-text');
    if (loadingText) {
      loadingText.textContent = `Error: ${message}`;
      loadingText.style.color = '#ff0066';
    }
  }
}

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', async () => {
  try {
    // Create and initialize the game engine
    const gameEngine = new GameEngine();

    // Initialize the engine
    await gameEngine.init();

    // Show the canvas
    gameEngine.showCanvas();

    // Start the game loop
    gameEngine.start();

    // Setup resize handler
    window.addEventListener('resize', () => gameEngine.handleResize());

    logger.info('Task 1.2 completed successfully - Rotating cube should be visible');

    // Export for debugging in development
    if (typeof __DEV__ !== 'undefined' && __DEV__) {
      window.gameEngine = gameEngine;
    }

  } catch (error) {
    console.error('Failed to initialize GameEngine:', error);

    // Show error in the loading screen
    const loadingText = document.getElementById('loading-text');
    if (loadingText) {
      loadingText.textContent = `Error: ${error.message}`;
      loadingText.style.color = '#ff0066';
    }
  }
});
