/**
 * NEON SHARD - Main Entry Point
 *
 * Task 1.1: Create the entry point. Initialize a Three.js WebGLRenderer
 * (requesting a WebGL2 context), a Scene, and a PerspectiveCamera.
 *
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

// Test if script is loading
console.log('NEON SHARD: Script loading...');

import * as THREE from 'three';

// Initialize logger for basic console output
const logger = {
  info: (msg) => console.log(`[NEON SHARD] ${msg}`),
  error: (msg) => console.error(`[NEON SHARD] ${msg}`),
  warn: (msg) => console.warn(`[NEON SHARD] ${msg}`)
};

/**
 * Simple application class for Task 1.1
 * Only initializes Three.js renderer, scene, and camera
 */
class Application {
  constructor() {
    this.renderer = null;
    this.scene = null;
    this.camera = null;
    this.canvas = null;
  }

  /**
   * Initialize the basic Three.js setup
   */
  async init() {
    try {
      logger.info('Initializing NEON SHARD - Task 1.1...');

      // Check if Three.js is available
      if (typeof THREE === 'undefined') {
        throw new Error('Three.js library not loaded');
      }
      logger.info('Three.js library loaded successfully');

      // Check WebGL 2.0 support
      if (!this.checkWebGLSupport()) {
        throw new Error('WebGL 2.0 is not supported on this device');
      }
      logger.info('WebGL 2.0 support confirmed');

      // Initialize Three.js components
      logger.info('Initializing renderer...');
      this.initRenderer();

      logger.info('Initializing scene...');
      this.initScene();

      logger.info('Initializing camera...');
      this.initCamera();

      // Show the canvas
      logger.info('Showing canvas...');
      this.showCanvas();

      logger.info('Task 1.1 completed successfully - Black canvas should be visible');

    } catch (error) {
      logger.error(`Failed to initialize: ${error.message}`);
      console.error('Full error:', error);
      this.showError(error.message);
    }
  }

  /**
   * Check if WebGL 2.0 is supported
   */
  checkWebGLSupport() {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2');
    return !!gl;
  }

  /**
   * Initialize the Three.js renderer with WebGL 2.0 context
   */
  initRenderer() {
    this.canvas = document.getElementById('game-canvas');

    if (!this.canvas) {
      throw new Error('Canvas element with id "game-canvas" not found');
    }

    // Create WebGL renderer requesting WebGL 2.0 context
    this.renderer = new THREE.WebGLRenderer({
      canvas: this.canvas,
      antialias: true,
      alpha: false,
      powerPreference: 'high-performance'
    });

    // Configure renderer
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

    // Verify WebGL 2.0 context
    const gl = this.renderer.getContext();
    if (gl.getParameter(gl.VERSION).includes('WebGL 2.0')) {
      logger.info('WebGL 2.0 context created successfully');
    } else {
      logger.warn('WebGL 2.0 not available, falling back to WebGL 1.0');
    }
  }

  /**
   * Initialize the Three.js scene
   */
  initScene() {
    this.scene = new THREE.Scene();
    // Set background to black as specified in verification
    this.scene.background = new THREE.Color(0x000000);
  }

  /**
   * Initialize the perspective camera
   */
  initCamera() {
    this.camera = new THREE.PerspectiveCamera(
      75, // FOV
      window.innerWidth / window.innerHeight, // Aspect ratio
      0.1, // Near plane
      1000 // Far plane
    );

    // Position camera at origin for now
    this.camera.position.set(0, 0, 0);
  }

  /**
   * Show the canvas and hide loading screen
   */
  showCanvas() {
    const loadingScreen = document.getElementById('loading-screen');

    // Hide loading screen
    if (loadingScreen) {
      loadingScreen.classList.add('hidden');
    }

    // Show canvas
    this.canvas.classList.remove('hidden');

    // Render the black scene once
    this.renderer.render(this.scene, this.camera);
  }

  /**
   * Show error message
   */
  showError(message) {
    const loadingText = document.getElementById('loading-text');
    if (loadingText) {
      loadingText.textContent = `Error: ${message}`;
      loadingText.style.color = '#ff0066';
    }
  }

  /**
   * Handle window resize
   */
  handleResize() {
    if (this.renderer && this.camera) {
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(window.innerWidth, window.innerHeight);
      this.renderer.render(this.scene, this.camera);
    }
  }
}

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', () => {
  // Create and initialize the application
  const app = new Application();

  // Setup resize handler
  window.addEventListener('resize', () => app.handleResize());

  // Start the application
  app.init().catch((error) => {
    console.error('Failed to initialize application:', error);
    // Also show error in the loading screen
    const loadingText = document.getElementById('loading-text');
    if (loadingText) {
      loadingText.textContent = `Error: ${error.message}`;
      loadingText.style.color = '#ff0066';
    }
  });

  // Export for debugging in development
  if (typeof __DEV__ !== 'undefined' && __DEV__) {
    window.app = app;
  }
});
