/**
 * NEON SHARD - Main Entry Point
 *
 * Task 1.3: Basic Asset Loader - Create a simple manager to load GLTF models and textures.
 * Implement a promise-based system to know when all critical assets are loaded.
 *
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

// Test if script is loading
console.log('NEON SHARD: Script loading...');

import * as THREE from 'three';
import { AssetLoader } from './core/AssetLoader.js';
import { InputManager } from './systems/InputManager.js';
import { PlayerController } from './systems/PlayerController.js';
import { PhysicsEngine } from './core/PhysicsEngine.js';
import { WeaponSystem } from './systems/WeaponSystem.js';
import { EnemyAI } from './systems/EnemyAI.js';

// Initialize logger for basic console output
const logger = {
  info: (msg) => console.log(`[NEON SHARD] ${msg}`),
  error: (msg) => console.error(`[NEON SHARD] ${msg}`),
  warn: (msg) => console.warn(`[NEON SHARD] ${msg}`)
};

/**
 * GameEngine class for Task 1.7
 * Implements weapon system with firing mechanics and projectile physics
 */
class GameEngine {
  constructor() {
    // Core Three.js components
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.canvas = null;

    // Game loop variables
    this.isRunning = false;
    this.lastTime = 0;
    this.deltaTime = 0;
    this.frameCount = 0;

    // Systems
    this.assetLoader = null;
    this.inputManager = null;
    this.playerController = null;
    this.physicsEngine = null;
    this.weaponSystem = null;

    // Game objects
    this.loadedModel = null;
    this.testCube = null;
    this.ground = null;

    // Physics bodies
    this.playerBody = null;
  }

  /**
   * Initialize the game engine
   */
  async init() {
    try {
      logger.info('Initializing GameEngine - Task 1.7...');

      // Check if Three.js is available
      if (typeof THREE === 'undefined') {
        throw new Error('Three.js library not loaded');
      }
      logger.info('Three.js library loaded successfully');

      // Check WebGL 2.0 support
      if (!this.checkWebGLSupport()) {
        throw new Error('WebGL 2.0 is not supported on this device');
      }
      logger.info('WebGL 2.0 support confirmed');

      // Initialize Three.js components
      this.initRenderer();
      this.initScene();
      this.initCamera();
      this.addGround(); // Add ground for physics testing

      // Initialize physics engine
      this.physicsEngine = new PhysicsEngine();
      await this.physicsEngine.init();
      this.setupPhysicsHandlers();

      // Initialize input manager
      this.inputManager = new InputManager(this.canvas);
      await this.inputManager.init();
      this.setupInputHandlers();

      // Initialize player controller with physics
      this.playerController = new PlayerController(this.camera, this.inputManager);
      await this.playerController.init();
      this.setupPlayerHandlers();

      // Create player physics body
      this.createPlayerPhysics();

      // Initialize weapon system
      this.weaponSystem = new WeaponSystem(this.camera, this.physicsEngine, this.scene);
      await this.weaponSystem.init();
      this.setupWeaponHandlers();

      // Initialize asset loader
      this.assetLoader = new AssetLoader();

      // Load test assets
      await this.loadTestAssets();

      logger.info('GameEngine initialized successfully');

    } catch (error) {
      logger.error(`Failed to initialize GameEngine: ${error.message}`);
      console.error('Full error:', error);
      throw error;
    }
  }

  /**
   * Check if WebGL 2.0 is supported
   */
  checkWebGLSupport() {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2');
    return !!gl;
  }

  /**
   * Initialize the Three.js renderer with WebGL 2.0 context
   */
  initRenderer() {
    this.canvas = document.getElementById('game-canvas');

    if (!this.canvas) {
      throw new Error('Canvas element with id "game-canvas" not found');
    }

    this.renderer = new THREE.WebGLRenderer({
      canvas: this.canvas,
      antialias: true,
      alpha: false,
      powerPreference: 'high-performance'
    });

    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

    const gl = this.renderer.getContext();
    if (gl.getParameter(gl.VERSION).includes('WebGL 2.0')) {
      logger.info('WebGL 2.0 context created successfully');
    } else {
      logger.warn('WebGL 2.0 not available, falling back to WebGL 1.0');
    }
  }

  /**
   * Initialize the Three.js scene
   */
  initScene() {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x000000);
  }

  /**
   * Initialize the perspective camera
   */
  initCamera() {
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    // Initial position will be set by PlayerController
    this.camera.position.set(0, 1.8, 0);
  }

  /**
   * Add a ground plane for physics testing
   */
  addGround() {
    // Create a large ground plane
    const groundGeometry = new THREE.PlaneGeometry(100, 100);
    const groundMaterial = new THREE.MeshBasicMaterial({
      color: 0x333333,
      wireframe: true
    });

    this.ground = new THREE.Mesh(groundGeometry, groundMaterial);
    this.ground.rotation.x = -Math.PI / 2; // Rotate to be horizontal
    this.ground.position.y = 0;
    this.scene.add(this.ground);

    logger.info('Ground plane added to scene');
  }

  /**
   * Setup input event handlers for Task 1.4 verification
   */
  setupInputHandlers() {
    logger.info('Setting up input handlers...');

    // Keyboard input handlers
    this.inputManager.on('keyPressed', (data) => {
      logger.info(`🎮 INPUT: Key pressed - ${data.action} (${data.code})`);
    });

    this.inputManager.on('keyReleased', (data) => {
      logger.info(`🎮 INPUT: Key released - ${data.action} (${data.code})`);
    });

    // Mouse input handlers
    this.inputManager.on('mousePressed', (data) => {
      logger.info(`🖱️ INPUT: Mouse ${data.button} pressed - ${data.action}`);
    });

    this.inputManager.on('mouseReleased', (data) => {
      logger.info(`🖱️ INPUT: Mouse ${data.button} released - ${data.action}`);
    });

    this.inputManager.on('mouseMove', (data) => {
      if (data.locked) {
        // Only log significant mouse movements to avoid spam
        if (Math.abs(data.deltaX) > 0.001 || Math.abs(data.deltaY) > 0.001) {
          logger.debug(`🖱️ INPUT: Mouse moved (locked) - deltaX: ${data.deltaX.toFixed(4)}, deltaY: ${data.deltaY.toFixed(4)}`);
        }
      } else {
        logger.debug(`🖱️ INPUT: Mouse moved - x: ${data.x}, y: ${data.y}`);
      }
    });

    // Pointer lock handlers
    this.inputManager.on('pointerLockChanged', (data) => {
      if (data.locked) {
        logger.info('🔒 INPUT: Pointer lock ACTIVATED - FPS mouse look enabled');
      } else {
        logger.info('🔓 INPUT: Pointer lock DEACTIVATED');
      }
    });

    this.inputManager.on('pointerLockError', () => {
      logger.error('❌ INPUT: Pointer lock failed');
    });

    logger.info('Input handlers setup complete');
  }

  /**
   * Setup player controller event handlers for Task 1.5 verification
   */
  setupPlayerHandlers() {
    logger.info('Setting up player event handlers...');

    // Player movement events
    this.playerController.on('moving', (data) => {
      // Only log occasionally to avoid spam
      if (Math.random() < 0.01) { // 1% chance per frame
        logger.debug(`🏃 PLAYER: Moving at position (${data.position.x.toFixed(2)}, ${data.position.y.toFixed(2)}, ${data.position.z.toFixed(2)})`);
      }
    });

    this.playerController.on('landed', () => {
      logger.info('🏃 PLAYER: Landed on ground');
    });

    this.playerController.on('falling', () => {
      logger.info('🏃 PLAYER: Started falling');
    });

    logger.info('Player event handlers setup complete');
  }

  /**
   * Setup weapon system event handlers for Task 1.7 verification
   */
  setupWeaponHandlers() {
    logger.info('Setting up weapon event handlers...');

    // Weapon firing events
    this.weaponSystem.on('fired', (data) => {
      logger.info(`🔫 WEAPON: ${data.weapon.name} fired! Ammo: ${data.weapon.currentAmmo}/${data.weapon.maxAmmo}`);
    });

    this.weaponSystem.on('weaponSwitched', (data) => {
      logger.info(`🔫 WEAPON: Switched to ${data.weapon.name}`);
    });

    this.weaponSystem.on('reloadStarted', (data) => {
      logger.info(`🔫 WEAPON: ${data.weapon.name} reloading...`);
    });

    this.weaponSystem.on('reloadCompleted', (data) => {
      logger.info(`🔫 WEAPON: ${data.weapon.name} reload complete!`);
    });

    // Integrate weapon controls with input manager
    this.inputManager.on('keyPressed', (data) => {
      switch (data.action) {
        case 'weapon1':
          this.weaponSystem.switchToWeaponNumber(1);
          break;
        case 'weapon2':
          this.weaponSystem.switchToWeaponNumber(2);
          break;
        case 'weapon3':
          this.weaponSystem.switchToWeaponNumber(3);
          break;
        case 'weapon4':
          this.weaponSystem.switchToWeaponNumber(4);
          break;
        case 'weapon5':
          this.weaponSystem.switchToWeaponNumber(5);
          break;
        case 'reload':
          this.weaponSystem.reload();
          break;
      }
    });

    this.inputManager.on('mousePressed', (data) => {
      if (data.action === 'fire') {
        this.weaponSystem.startFiring();
      }
    });

    this.inputManager.on('mouseReleased', (data) => {
      if (data.action === 'fire') {
        this.weaponSystem.stopFiring();
      }
    });

    logger.info('Weapon event handlers setup complete');
  }

  /**
   * Setup physics engine event handlers for Task 1.6 verification
   */
  setupPhysicsHandlers() {
    logger.info('Setting up physics event handlers...');

    // Log physics statistics occasionally
    setInterval(() => {
      const stats = this.physicsEngine.getStats();
      logger.debug(`🔬 PHYSICS: Bodies: ${stats.bodies}, Contacts: ${stats.contacts}`);
    }, 5000); // Every 5 seconds

    logger.info('Physics event handlers setup complete');
  }

  /**
   * Create player physics body and integrate with PlayerController
   */
  createPlayerPhysics() {
    logger.info('Creating player physics body...');

    // Create player physics body
    this.playerBody = this.physicsEngine.createPlayerBody(this.playerController.getPosition());

    // Update PlayerController to use physics
    this.playerController.setPhysicsBody(this.playerBody, this.physicsEngine);

    logger.info('Player physics body created and integrated');
  }

  /**
   * Load test assets for Task 1.3 verification
   */
  async loadTestAssets() {
    logger.info('Loading test assets...');

    // Define assets to load
    const assetsToLoad = [
      {
        name: 'test-crate',
        type: 'model',
        path: '/assets/models/test-crate.gltf'
      }
    ];

    // Setup progress tracking
    this.assetLoader.on('progress', (progress) => {
      logger.info(`Asset loading progress: ${progress.loaded}/${progress.total} (${Math.round(progress.percentage)}%)`);

      // Update loading screen if available
      const loadingProgress = document.getElementById('loading-progress');
      const loadingText = document.getElementById('loading-text');

      if (loadingProgress) {
        loadingProgress.style.width = `${progress.percentage}%`;
      }

      if (loadingText) {
        loadingText.textContent = `Loading ${progress.item}... ${Math.round(progress.percentage)}%`;
      }
    });

    try {
      // Load all assets
      await this.assetLoader.loadAssets(assetsToLoad);

      // Get the loaded crate model
      const crateAsset = this.assetLoader.getAsset('test-crate');
      if (crateAsset && crateAsset.scene) {
        this.loadedModel = crateAsset.scene.clone();
        this.loadedModel.position.set(2, 5, 0); // Position above ground to test physics
        this.loadedModel.scale.set(0.5, 0.5, 0.5); // Make it smaller
        this.scene.add(this.loadedModel);

        // Add physics body to the crate
        this.physicsEngine.createRigidBody(this.loadedModel, {
          mass: 1,
          shape: 'box'
        });

        logger.info('Test crate model added to scene with physics');
      }

      // Also add the test cube for comparison
      this.addTestCube();

      logger.info('All test assets loaded successfully');

    } catch (error) {
      logger.error(`Failed to load test assets: ${error.message}`);
      // Fall back to just the test cube
      this.addTestCube();
      throw error;
    }
  }

  /**
   * Add a test cube to verify frame-rate independent rotation and physics
   */
  addTestCube() {
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial({
      color: 0x00ffff,
      wireframe: true
    });

    this.testCube = new THREE.Mesh(geometry, material);
    this.testCube.position.set(-2, 5, 0); // Position above ground to test physics
    this.scene.add(this.testCube);

    // Add physics body to the test cube
    this.physicsEngine.createRigidBody(this.testCube, {
      mass: 1,
      shape: 'box'
    });

    logger.info('Test cube added to scene with physics');
  }

  /**
   * Start the game loop
   */
  start() {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.lastTime = performance.now();

    logger.info('Starting game loop...');
    this.gameLoop();
  }

  /**
   * Main game loop using requestAnimationFrame
   */
  gameLoop() {
    if (!this.isRunning) {
      return;
    }

    // Calculate deltaTime for frame-rate independent logic
    const currentTime = performance.now();
    this.deltaTime = (currentTime - this.lastTime) / 1000; // Convert to seconds
    this.lastTime = currentTime;

    // Cap delta time to prevent large jumps
    this.deltaTime = Math.min(this.deltaTime, 1/30);

    // Update game logic
    this.update(this.deltaTime);

    // Render the scene
    this.render();

    // Continue the loop
    requestAnimationFrame(() => this.gameLoop());

    this.frameCount++;
  }

  /**
   * Update method - called every frame with deltaTime
   */
  update(deltaTime) {
    // Update physics engine first
    if (this.physicsEngine) {
      this.physicsEngine.update(deltaTime);
    }

    // Update input manager
    if (this.inputManager) {
      this.inputManager.update(deltaTime);
    }

    // Update player controller
    if (this.playerController) {
      this.playerController.update(deltaTime);
    }

    // Update weapon system
    if (this.weaponSystem) {
      this.weaponSystem.update(deltaTime);
    }

    // Note: Test cube and loaded model now use physics for movement
    // Their positions are automatically updated by the physics engine
    // Projectiles are also physics-controlled and managed by WeaponSystem
  }

  /**
   * Render method - renders the scene
   */
  render() {
    this.renderer.render(this.scene, this.camera);
  }

  /**
   * Handle window resize
   */
  handleResize() {
    if (this.renderer && this.camera) {
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
  }

  /**
   * Show the canvas and hide loading screen
   */
  showCanvas() {
    const loadingScreen = document.getElementById('loading-screen');

    if (loadingScreen) {
      loadingScreen.classList.add('hidden');
    }

    this.canvas.classList.remove('hidden');
  }

  /**
   * Show error message
   */
  showError(message) {
    const loadingText = document.getElementById('loading-text');
    if (loadingText) {
      loadingText.textContent = `Error: ${message}`;
      loadingText.style.color = '#ff0066';
    }
  }
}

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', async () => {
  try {
    // Create and initialize the game engine
    const gameEngine = new GameEngine();

    // Initialize the engine
    await gameEngine.init();

    // Show the canvas
    gameEngine.showCanvas();

    // Start the game loop
    gameEngine.start();

    // Setup resize handler
    window.addEventListener('resize', () => gameEngine.handleResize());

    logger.info('Task 1.7 completed successfully - Weapon system active! Use 1-5 to switch weapons, HOLD LMB for continuous fire, R to reload!');

    // Export for debugging in development
    if (typeof __DEV__ !== 'undefined' && __DEV__) {
      window.gameEngine = gameEngine;
    }

  } catch (error) {
    console.error('Failed to initialize GameEngine:', error);

    // Show error in the loading screen
    const loadingText = document.getElementById('loading-text');
    if (loadingText) {
      loadingText.textContent = `Error: ${error.message}`;
      loadingText.style.color = '#ff0066';
    }
  }
});
