/**
 * NEON SHARD - Main Entry Point
 * 
 * This is the main entry point for the client-side application.
 * It initializes the game engine and handles the application lifecycle.
 * 
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

import { GameEngine } from './core/GameEngine.js';
import { AssetLoader } from './core/AssetLoader.js';
import { LoadingScreen } from './ui/LoadingScreen.js';
import { ErrorHandler } from './utils/ErrorHandler.js';
import { Logger } from './utils/Logger.js';
import { PerformanceMonitor } from './utils/PerformanceMonitor.js';

// Initialize logger
const logger = new Logger('Main');

// Global error handling
window.addEventListener('error', (event) => {
  ErrorHandler.handleError(event.error, 'Global Error');
});

window.addEventListener('unhandledrejection', (event) => {
  ErrorHandler.handleError(event.reason, 'Unhandled Promise Rejection');
});

/**
 * Application class that manages the entire game lifecycle
 */
class Application {
  constructor() {
    this.gameEngine = null;
    this.assetLoader = null;
    this.loadingScreen = null;
    this.performanceMonitor = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the application
   */
  async init() {
    try {
      logger.info('Initializing NEON SHARD...');
      
      // Initialize loading screen
      this.loadingScreen = new LoadingScreen();
      this.loadingScreen.show();
      
      // Initialize performance monitoring in development
      if (__DEV__) {
        this.performanceMonitor = new PerformanceMonitor();
        this.performanceMonitor.start();
      }
      
      // Check WebGL support
      if (!this.checkWebGLSupport()) {
        throw new Error('WebGL 2.0 is not supported on this device');
      }
      
      // Initialize asset loader
      this.assetLoader = new AssetLoader();
      this.setupAssetLoaderEvents();
      
      // Load critical assets
      await this.loadCriticalAssets();
      
      // Initialize game engine
      this.gameEngine = new GameEngine();
      await this.gameEngine.init();
      
      // Hide loading screen and show game
      this.loadingScreen.hide();
      this.showGame();
      
      // Start the game loop
      this.gameEngine.start();
      
      this.isInitialized = true;
      logger.info('NEON SHARD initialized successfully');
      
    } catch (error) {
      ErrorHandler.handleError(error, 'Application Initialization');
      this.showErrorScreen(error);
    }
  }

  /**
   * Check if WebGL 2.0 is supported
   */
  checkWebGLSupport() {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2');
    return !!gl;
  }

  /**
   * Setup asset loader event handlers
   */
  setupAssetLoaderEvents() {
    this.assetLoader.on('progress', (progress) => {
      this.loadingScreen.updateProgress(progress.loaded / progress.total);
      this.loadingScreen.updateText(`Loading ${progress.item}...`);
    });

    this.assetLoader.on('error', (error) => {
      logger.error('Asset loading error:', error);
      this.loadingScreen.updateText('Error loading assets...');
    });
  }

  /**
   * Load critical assets required for the game
   */
  async loadCriticalAssets() {
    const criticalAssets = [
      // Models
      { type: 'model', path: '/assets/models/weapons/shard-pistol.gltf', name: 'shard-pistol' },
      { type: 'model', path: '/assets/models/environment/test-level.gltf', name: 'test-level' },
      
      // Textures
      { type: 'texture', path: '/assets/textures/ui/crosshair.png', name: 'crosshair' },
      { type: 'texture', path: '/assets/textures/effects/muzzle-flash.png', name: 'muzzle-flash' },
      
      // Audio
      { type: 'audio', path: '/assets/audio/weapons/pistol-fire.wav', name: 'pistol-fire' },
      { type: 'audio', path: '/assets/audio/ui/menu-click.wav', name: 'menu-click' },
      
      // Shaders
      { type: 'shader', path: '/src/shaders/neon.vert', name: 'neon-vertex' },
      { type: 'shader', path: '/src/shaders/neon.frag', name: 'neon-fragment' }
    ];

    await this.assetLoader.loadAssets(criticalAssets);
  }

  /**
   * Show the game canvas and hide other screens
   */
  showGame() {
    const canvas = document.getElementById('game-canvas');
    const hudContainer = document.getElementById('hud-container');
    
    canvas.classList.remove('hidden');
    hudContainer.classList.remove('hidden');
    
    // Focus the canvas for input
    canvas.focus();
  }

  /**
   * Show error screen with details
   */
  showErrorScreen(error) {
    this.loadingScreen.showError(error.message);
    logger.error('Fatal error:', error);
  }

  /**
   * Handle window resize
   */
  handleResize() {
    if (this.gameEngine && this.isInitialized) {
      this.gameEngine.handleResize();
    }
  }

  /**
   * Handle visibility change (tab switching, minimizing)
   */
  handleVisibilityChange() {
    if (this.gameEngine && this.isInitialized) {
      if (document.hidden) {
        this.gameEngine.pause();
      } else {
        this.gameEngine.resume();
      }
    }
  }

  /**
   * Cleanup and shutdown
   */
  shutdown() {
    logger.info('Shutting down application...');
    
    if (this.gameEngine) {
      this.gameEngine.shutdown();
    }
    
    if (this.performanceMonitor) {
      this.performanceMonitor.stop();
    }
    
    this.isInitialized = false;
  }
}

// Create and initialize the application
const app = new Application();

// Setup event listeners
window.addEventListener('resize', () => app.handleResize());
document.addEventListener('visibilitychange', () => app.handleVisibilityChange());
window.addEventListener('beforeunload', () => app.shutdown());

// Start the application
app.init().catch((error) => {
  console.error('Failed to initialize application:', error);
});

// Export for debugging in development
if (__DEV__) {
  window.app = app;
}
