/**
 * Logger utility for consistent logging across the application
 * 
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

export class Logger {
  constructor(context = 'App') {
    this.context = context;
    this.isDevelopment = __DEV__;
  }

  /**
   * Log info message
   */
  info(message, ...args) {
    if (this.isDevelopment) {
      console.log(`[${this.context}] ${message}`, ...args);
    }
  }

  /**
   * Log warning message
   */
  warn(message, ...args) {
    console.warn(`[${this.context}] ${message}`, ...args);
  }

  /**
   * Log error message
   */
  error(message, ...args) {
    console.error(`[${this.context}] ${message}`, ...args);
  }

  /**
   * Log debug message (development only)
   */
  debug(message, ...args) {
    if (this.isDevelopment) {
      console.debug(`[${this.context}] ${message}`, ...args);
    }
  }
}
