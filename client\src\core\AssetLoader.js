/**
 * Asset<PERSON>oa<PERSON> - Handles loading and caching of game assets
 *
 * Task 1.3: Implement GLTF and texture loading with Three.js loaders
 *
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { EventEmitter } from '../utils/EventEmitter.js';
import { Logger } from '../utils/Logger.js';

const logger = new Logger('AssetLoader');

export class AssetLoader extends EventEmitter {
  constructor() {
    super();
    this.loadedAssets = new Map();
    this.loadingPromises = new Map();

    // Initialize Three.js loaders
    this.gltfLoader = new GLTFLoader();
    this.textureLoader = new THREE.TextureLoader();

    logger.info('AssetLoader initialized with Three.js loaders');
  }

  /**
   * Load multiple assets with progress tracking
   */
  async loadAssets(assets) {
    logger.info(`Loading ${assets.length} assets...`);

    const promises = assets.map(asset => this.loadAsset(asset));

    let loaded = 0;
    const total = assets.length;

    for (const promise of promises) {
      try {
        const result = await promise;
        loaded++;
        this.emit('progress', {
          loaded,
          total,
          item: result.name || 'Asset',
          percentage: (loaded / total) * 100
        });
        logger.debug(`Loaded asset ${loaded}/${total}`);
      } catch (error) {
        logger.error(`Failed to load asset: ${error.message}`);
        this.emit('error', error);
        throw error;
      }
    }

    logger.info(`Successfully loaded all ${total} assets`);
    return this.loadedAssets;
  }

  /**
   * Load a single asset
   */
  async loadAsset(asset) {
    if (this.loadedAssets.has(asset.name)) {
      return this.loadedAssets.get(asset.name);
    }

    if (this.loadingPromises.has(asset.name)) {
      return this.loadingPromises.get(asset.name);
    }

    const promise = this._loadAssetByType(asset);
    this.loadingPromises.set(asset.name, promise);

    try {
      const result = await promise;
      this.loadedAssets.set(asset.name, result);
      this.loadingPromises.delete(asset.name);
      return result;
    } catch (error) {
      this.loadingPromises.delete(asset.name);
      throw error;
    }
  }

  /**
   * Load asset based on type
   */
  async _loadAssetByType(asset) {
    switch (asset.type) {
      case 'model':
        return this._loadModel(asset.path);
      case 'texture':
        return this._loadTexture(asset.path);
      case 'audio':
        return this._loadAudio(asset.path);
      case 'shader':
        return this._loadShader(asset.path);
      default:
        throw new Error(`Unknown asset type: ${asset.type}`);
    }
  }

  /**
   * Load GLTF model using Three.js GLTFLoader
   */
  async _loadModel(path) {
    logger.debug(`Loading GLTF model: ${path}`);

    try {
      const gltf = await this.gltfLoader.loadAsync(path);
      logger.info(`Successfully loaded GLTF model: ${path}`);

      return {
        type: 'model',
        path: path,
        gltf: gltf,
        scene: gltf.scene,
        animations: gltf.animations,
        asset: gltf.asset
      };
    } catch (error) {
      logger.error(`Failed to load GLTF model ${path}: ${error.message}`);
      throw new Error(`Failed to load GLTF model: ${error.message}`);
    }
  }

  /**
   * Load texture using Three.js TextureLoader
   */
  async _loadTexture(path) {
    logger.debug(`Loading texture: ${path}`);

    try {
      const texture = await this.textureLoader.loadAsync(path);
      logger.info(`Successfully loaded texture: ${path}`);

      return {
        type: 'texture',
        path: path,
        texture: texture
      };
    } catch (error) {
      logger.error(`Failed to load texture ${path}: ${error.message}`);
      throw new Error(`Failed to load texture: ${error.message}`);
    }
  }

  /**
   * Load audio (placeholder)
   */
  async _loadAudio(path) {
    logger.debug(`Loading audio: ${path}`);
    // TODO: Implement audio loading with Web Audio API
    return { type: 'audio', path };
  }

  /**
   * Load shader (placeholder)
   */
  async _loadShader(path) {
    logger.debug(`Loading shader: ${path}`);
    // TODO: Implement shader loading
    return { type: 'shader', path };
  }

  /**
   * Get loaded asset
   */
  getAsset(name) {
    return this.loadedAssets.get(name);
  }
}
