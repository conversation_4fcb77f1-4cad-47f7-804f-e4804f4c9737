/**
 * As<PERSON><PERSON>oader - Handles loading and caching of game assets
 * 
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

import { EventEmitter } from '../utils/EventEmitter.js';
import { Logger } from '../utils/Logger.js';

const logger = new Logger('AssetLoader');

export class AssetLoader extends EventEmitter {
  constructor() {
    super();
    this.loadedAssets = new Map();
    this.loadingPromises = new Map();
  }

  /**
   * Load multiple assets
   */
  async loadAssets(assets) {
    const promises = assets.map(asset => this.loadAsset(asset));
    
    let loaded = 0;
    const total = assets.length;
    
    for (const promise of promises) {
      try {
        await promise;
        loaded++;
        this.emit('progress', { loaded, total, item: 'Loading...' });
      } catch (error) {
        this.emit('error', error);
        throw error;
      }
    }
    
    return this.loadedAssets;
  }

  /**
   * Load a single asset
   */
  async loadAsset(asset) {
    if (this.loadedAssets.has(asset.name)) {
      return this.loadedAssets.get(asset.name);
    }

    if (this.loadingPromises.has(asset.name)) {
      return this.loadingPromises.get(asset.name);
    }

    const promise = this._loadAssetByType(asset);
    this.loadingPromises.set(asset.name, promise);

    try {
      const result = await promise;
      this.loadedAssets.set(asset.name, result);
      this.loadingPromises.delete(asset.name);
      return result;
    } catch (error) {
      this.loadingPromises.delete(asset.name);
      throw error;
    }
  }

  /**
   * Load asset based on type
   */
  async _loadAssetByType(asset) {
    switch (asset.type) {
      case 'model':
        return this._loadModel(asset.path);
      case 'texture':
        return this._loadTexture(asset.path);
      case 'audio':
        return this._loadAudio(asset.path);
      case 'shader':
        return this._loadShader(asset.path);
      default:
        throw new Error(`Unknown asset type: ${asset.type}`);
    }
  }

  /**
   * Load GLTF model (placeholder)
   */
  async _loadModel(path) {
    logger.debug(`Loading model: ${path}`);
    // TODO: Implement GLTF loading with Three.js GLTFLoader
    return { type: 'model', path };
  }

  /**
   * Load texture (placeholder)
   */
  async _loadTexture(path) {
    logger.debug(`Loading texture: ${path}`);
    // TODO: Implement texture loading with Three.js TextureLoader
    return { type: 'texture', path };
  }

  /**
   * Load audio (placeholder)
   */
  async _loadAudio(path) {
    logger.debug(`Loading audio: ${path}`);
    // TODO: Implement audio loading with Web Audio API
    return { type: 'audio', path };
  }

  /**
   * Load shader (placeholder)
   */
  async _loadShader(path) {
    logger.debug(`Loading shader: ${path}`);
    // TODO: Implement shader loading
    return { type: 'shader', path };
  }

  /**
   * Get loaded asset
   */
  getAsset(name) {
    return this.loadedAssets.get(name);
  }
}
