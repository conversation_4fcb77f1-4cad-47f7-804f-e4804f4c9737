# NEON SHARD

A fast-paced, competitive arena first-person shooter built for the browser using modern web technologies.

## 🎮 Game Overview

NEON SHARD is a love letter to classic arena shooters of the late '90s, reimagined with a stylized, low-poly "cyber-retro" aesthetic. Built with Three.js, Cannon-es physics, and Socket.IO for real-time multiplayer action.

### Core Features

- **Fluid Movement**: Acrobatic movement with dash mechanics and precise controls
- **Tactical Combat**: High-impact weapons with distinct roles and feel
- **Stylized Visuals**: Low-poly cyber-retro aesthetic optimized for 60 FPS
- **Competitive Multiplayer**: Browser-based with low-latency networking

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- npm 9+
- Modern browser with WebGL 2.0 support

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/neon-shard.git
   cd neon-shard
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Setup environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start development servers**
   ```bash
   npm run dev
   ```

   This starts both the client (port 3000) and server (port 3001) in development mode.

5. **Open your browser**
   Navigate to `http://localhost:3000`

## 🛠️ Development

### Project Structure

```
neon-shard/
├── client/                 # Client-side code
│   ├── src/
│   │   ├── core/          # Core engine systems
│   │   ├── systems/       # Game systems (input, audio, etc.)
│   │   ├── ui/            # User interface components
│   │   ├── utils/         # Utility functions
│   │   └── shaders/       # GLSL shaders
│   ├── assets/            # Game assets
│   └── index.html         # Entry point
├── server/                # Server-side code
│   ├── core/             # Server core systems
│   ├── systems/          # Server game systems
│   └── utils/            # Server utilities
├── tests/                # Test files
└── docs/                 # Documentation
```

### Available Scripts

- `npm run dev` - Start both client and server in development mode
- `npm run dev:client` - Start only the client development server
- `npm run dev:server` - Start only the game server
- `npm run build` - Build the client for production
- `npm run test` - Run unit tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:e2e` - Run end-to-end tests
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

### Development Workflow

1. **Follow the PRD**: All implementation must adhere to the PRD specifications
2. **One Task at a Time**: Focus on current sub-task from the implementation document
3. **Test Everything**: Write unit tests and perform manual verification
4. **Performance First**: Be mindful of performance in the game loop
5. **Clean Code**: Follow the established coding standards

### Testing

The project uses a comprehensive testing strategy:

- **Unit Tests**: Jest for testing individual modules
- **Integration Tests**: Testing system interactions
- **E2E Tests**: Playwright for browser automation
- **Performance Tests**: Automated performance benchmarking

Run tests with:
```bash
npm test                    # Unit tests
npm run test:coverage      # With coverage report
npm run test:e2e          # End-to-end tests
```

### Code Quality

The project enforces code quality through:

- **ESLint**: JavaScript linting with game-specific rules
- **Prettier**: Consistent code formatting
- **Husky**: Pre-commit hooks for quality checks
- **Lint-staged**: Run checks only on changed files

## 🏗️ Architecture

### Client Architecture

- **Three.js**: WebGL rendering and 3D graphics
- **Cannon-es**: Physics simulation
- **Socket.IO**: Real-time networking
- **Vite**: Fast development and optimized builds

### Server Architecture

- **Node.js**: Server runtime
- **Express**: HTTP server and API
- **Socket.IO**: WebSocket communication
- **Authoritative**: Server handles all game logic

### Key Systems

1. **GameEngine**: Core game loop and system coordination
2. **PhysicsEngine**: Collision detection and rigid body simulation
3. **PlayerController**: Input handling and player movement
4. **WeaponSystem**: Weapon mechanics and firing
5. **NetworkManager**: Client-server synchronization
6. **AudioManager**: 3D positional audio
7. **ParticleSystem**: Visual effects

## 🎯 Performance Targets

- **60 FPS**: Stable frame rate on target hardware
- **<150ms**: Network latency for responsive gameplay
- **<2MB**: Initial asset bundle size
- **<500ms**: Level load times

## 🔧 Configuration

### Environment Variables

See `.env.example` for all available configuration options.

Key settings:
- `PORT`: Server port (default: 3001)
- `MAX_PLAYERS`: Maximum players per server (default: 8)
- `TICK_RATE`: Server update frequency (default: 20 Hz)

### Build Configuration

The build system is configured through:
- `vite.config.js`: Client build configuration
- `package.json`: Scripts and dependencies
- `.eslintrc.js`: Code quality rules

## 📚 Documentation

- [PRD & Game Design Document](./Neon/PRD-GDD.md)
- [Agent Development Rules](./Neon/AgentRules.md)
- [API Documentation](./docs/api.md) (coming soon)
- [Asset Guidelines](./docs/assets.md) (coming soon)

## 🤝 Contributing

1. Follow the development workflow outlined above
2. Ensure all tests pass before submitting
3. Use conventional commit messages
4. Update documentation as needed

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🎮 Game Controls

- **WASD**: Movement
- **Mouse**: Look around
- **Left Click**: Fire weapon
- **Shift**: Sprint
- **Space**: Jump (double-tap for dash)
- **Ctrl**: Crouch
- **1-5**: Weapon selection
- **R**: Reload
- **Tab**: Scoreboard
- **Esc**: Menu

---

**Built with ❤️ for the arena shooter community**
