{"name": "neon-shard", "version": "1.0.0", "description": "A fast-paced, competitive arena first-person shooter built for the browser", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:server\"", "dev:client": "vite", "dev:server": "nodemon server/server.js", "build": "vite build", "build:server": "node scripts/build-server.js", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "start": "node server/server.js", "clean": "rimraf dist coverage .nyc_output", "validate": "npm run lint && npm run format:check && npm run test", "prepare": "husky install"}, "dependencies": {"three": "^0.158.0", "cannon-es": "^0.20.0", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "dotenv": "^16.3.1"}, "devDependencies": {"vite": "^5.0.0", "vite-plugin-glsl": "^1.1.2", "@vitejs/plugin-legacy": "^5.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@playwright/test": "^1.40.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest": "^27.6.0", "prettier": "^3.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "concurrently": "^8.2.2", "nodemon": "^3.0.2", "rimraf": "^5.0.5", "cross-env": "^7.0.3", "terser": "^5.24.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css,html}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["game", "fps", "multiplayer", "browser", "threejs", "webgl", "arena-shooter"], "author": "Neon Shard Development Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/neon-shard.git"}}