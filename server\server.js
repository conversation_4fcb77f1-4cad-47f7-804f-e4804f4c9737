/**
 * NEON SHARD - Game Server
 * 
 * Authoritative game server built with Node.js, Express, and Socket.IO
 * Handles game logic, physics, and player synchronization
 * 
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

import { GameServer } from './core/GameServer.js';
import { Logger } from './utils/Logger.js';
import { ErrorHandler } from './utils/ErrorHandler.js';

// Load environment variables
dotenv.config();

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize logger
const logger = new Logger('Server');

// Configuration
const config = {
  port: process.env.PORT || 3001,
  host: process.env.HOST || 'localhost',
  environment: process.env.NODE_ENV || 'development',
  corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  maxPlayers: parseInt(process.env.MAX_PLAYERS) || 8,
  tickRate: parseInt(process.env.TICK_RATE) || 20, // 20 Hz server tick rate
  enableMetrics: process.env.ENABLE_METRICS === 'true'
};

/**
 * Main server application class
 */
class ServerApplication {
  constructor() {
    this.app = express();
    this.httpServer = null;
    this.io = null;
    this.gameServer = null;
    this.isRunning = false;
  }

  /**
   * Initialize the server
   */
  async init() {
    try {
      logger.info('Initializing NEON SHARD Server...');
      
      // Setup Express middleware
      this.setupMiddleware();
      
      // Setup routes
      this.setupRoutes();
      
      // Create HTTP server
      this.httpServer = createServer(this.app);
      
      // Initialize Socket.IO
      this.initSocketIO();
      
      // Initialize game server
      this.gameServer = new GameServer(this.io, config);
      await this.gameServer.init();
      
      // Setup error handling
      this.setupErrorHandling();
      
      logger.info('Server initialized successfully');
      
    } catch (error) {
      logger.error('Failed to initialize server:', error);
      throw error;
    }
  }

  /**
   * Setup Express middleware
   */
  setupMiddleware() {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: false, // Disable for game assets
      crossOriginEmbedderPolicy: false
    }));
    
    // CORS configuration
    this.app.use(cors({
      origin: config.corsOrigin,
      credentials: true,
      methods: ['GET', 'POST'],
      allowedHeaders: ['Content-Type', 'Authorization']
    }));
    
    // Compression
    this.app.use(compression());
    
    // JSON parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    
    // Request logging in development
    if (config.environment === 'development') {
      this.app.use((req, res, next) => {
        logger.debug(`${req.method} ${req.path}`);
        next();
      });
    }
  }

  /**
   * Setup API routes
   */
  setupRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: config.environment
      });
    });

    // Server info endpoint
    this.app.get('/api/server-info', (req, res) => {
      res.json({
        maxPlayers: config.maxPlayers,
        currentPlayers: this.gameServer ? this.gameServer.getPlayerCount() : 0,
        tickRate: config.tickRate,
        version: process.env.npm_package_version || '1.0.0'
      });
    });

    // Game statistics endpoint
    this.app.get('/api/stats', (req, res) => {
      if (!this.gameServer) {
        return res.status(503).json({ error: 'Game server not ready' });
      }
      
      res.json(this.gameServer.getStatistics());
    });

    // Serve static files in production
    if (config.environment === 'production') {
      const staticPath = path.join(__dirname, '../dist');
      this.app.use(express.static(staticPath));
      
      // Serve index.html for all non-API routes
      this.app.get('*', (req, res) => {
        if (!req.path.startsWith('/api/')) {
          res.sendFile(path.join(staticPath, 'index.html'));
        } else {
          res.status(404).json({ error: 'API endpoint not found' });
        }
      });
    }

    // 404 handler for API routes
    this.app.use('/api/*', (req, res) => {
      res.status(404).json({ error: 'API endpoint not found' });
    });
  }

  /**
   * Initialize Socket.IO server
   */
  initSocketIO() {
    this.io = new SocketIOServer(this.httpServer, {
      cors: {
        origin: config.corsOrigin,
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling'],
      pingTimeout: 60000,
      pingInterval: 25000,
      upgradeTimeout: 30000,
      maxHttpBufferSize: 1e6, // 1MB
      allowEIO3: true
    });

    // Socket.IO middleware for authentication (if needed)
    this.io.use((socket, next) => {
      // Add authentication logic here if needed
      logger.debug(`Client connecting: ${socket.id}`);
      next();
    });

    // Connection handling
    this.io.on('connection', (socket) => {
      logger.info(`Client connected: ${socket.id}`);
      
      socket.on('disconnect', (reason) => {
        logger.info(`Client disconnected: ${socket.id}, reason: ${reason}`);
      });
    });
  }

  /**
   * Setup error handling
   */
  setupErrorHandling() {
    // Express error handler
    this.app.use((err, req, res, next) => {
      ErrorHandler.handleError(err, 'Express Error');
      res.status(500).json({ error: 'Internal server error' });
    });

    // Process error handlers
    process.on('uncaughtException', (error) => {
      ErrorHandler.handleError(error, 'Uncaught Exception');
      this.gracefulShutdown();
    });

    process.on('unhandledRejection', (reason, promise) => {
      ErrorHandler.handleError(reason, 'Unhandled Rejection');
    });

    // Graceful shutdown signals
    process.on('SIGTERM', () => {
      logger.info('SIGTERM received, shutting down gracefully');
      this.gracefulShutdown();
    });

    process.on('SIGINT', () => {
      logger.info('SIGINT received, shutting down gracefully');
      this.gracefulShutdown();
    });
  }

  /**
   * Start the server
   */
  async start() {
    try {
      await new Promise((resolve, reject) => {
        this.httpServer.listen(config.port, config.host, (error) => {
          if (error) {
            reject(error);
          } else {
            resolve();
          }
        });
      });

      this.isRunning = true;
      
      logger.info(`NEON SHARD Server running on ${config.host}:${config.port}`);
      logger.info(`Environment: ${config.environment}`);
      logger.info(`Max players: ${config.maxPlayers}`);
      logger.info(`Tick rate: ${config.tickRate} Hz`);
      
      // Start game server
      if (this.gameServer) {
        this.gameServer.start();
      }
      
    } catch (error) {
      logger.error('Failed to start server:', error);
      throw error;
    }
  }

  /**
   * Graceful shutdown
   */
  async gracefulShutdown() {
    if (!this.isRunning) {
      return;
    }

    logger.info('Starting graceful shutdown...');
    this.isRunning = false;

    try {
      // Stop game server
      if (this.gameServer) {
        await this.gameServer.stop();
      }

      // Close Socket.IO connections
      if (this.io) {
        this.io.close();
      }

      // Close HTTP server
      if (this.httpServer) {
        await new Promise((resolve) => {
          this.httpServer.close(resolve);
        });
      }

      logger.info('Graceful shutdown completed');
      process.exit(0);
      
    } catch (error) {
      logger.error('Error during shutdown:', error);
      process.exit(1);
    }
  }
}

// Create and start the server
const server = new ServerApplication();

async function main() {
  try {
    await server.init();
    await server.start();
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server
main();
