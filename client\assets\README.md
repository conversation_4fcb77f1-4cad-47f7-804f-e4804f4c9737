# Assets Directory

This directory contains all game assets organized by type.

## Structure

```
assets/
├── models/           # 3D models (GLTF format)
│   ├── weapons/     # Weapon models
│   ├── environment/ # Level geometry
│   └── characters/  # Player/AI models
├── textures/        # Texture files
│   ├── ui/         # UI textures
│   ├── effects/    # Effect textures
│   └── environment/# Environment textures
├── audio/           # Audio files
│   ├── weapons/    # Weapon sounds (WAV)
│   ├── ui/         # UI sounds (WAV)
│   └── music/      # Background music (MP3)
├── fonts/           # Font files
└── icons/           # Icon files
```

## Asset Guidelines

### Models
- Format: GLTF (.gltf or .glb)
- Weapon models: < 2,000 triangles
- Environment props: < 500 triangles
- Use low-poly style with clean geometry

### Textures
- Power-of-two dimensions (256x256, 512x512, 1024x1024)
- Weapon textures: max 512x512
- Environment textures: max 1024x1024
- Use PNG for transparency, JPG for opaque textures

### Audio
- Critical SFX: WAV format, 44.1kHz, 16-bit
- Music/ambient: MP3 format, 128-192 kbps
- Keep file sizes reasonable for web delivery

### Performance Budget
- Total initial assets: < 2MB
- Individual textures: < 256KB
- Individual audio files: < 100KB (SFX), < 1MB (music)

## Placeholder Assets

During development, use clearly marked placeholder assets:
- Simple colored cubes for models
- Solid color textures
- Generic beep sounds
- Placeholder text for fonts
