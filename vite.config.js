import { defineConfig } from 'vite';
import { resolve } from 'path';
import glsl from 'vite-plugin-glsl';
import legacy from '@vitejs/plugin-legacy';

export default defineConfig({
  plugins: [
    glsl({
      include: [
        '**/*.glsl',
        '**/*.wgsl',
        '**/*.vert',
        '**/*.frag',
        '**/*.vs',
        '**/*.fs'
      ],
      exclude: undefined,
      warnDuplicatedImports: true,
      defaultExtension: 'glsl',
      compress: false,
      watch: true
    }),
    legacy({
      targets: ['defaults', 'not IE 11']
    })
  ],
  
  root: 'client',
  
  build: {
    outDir: '../dist',
    emptyOutDir: true,
    sourcemap: true,
    target: 'es2020',
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'client/index.html')
      },
      output: {
        manualChunks: {
          'three': ['three'],
          'cannon': ['cannon-es'],
          'socket': ['socket.io-client']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  },
  
  server: {
    port: 3000,
    host: true,
    cors: true,
    proxy: {
      '/socket.io': {
        target: 'http://localhost:3001',
        ws: true,
        changeOrigin: true
      },
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true
      }
    }
  },
  
  preview: {
    port: 3000,
    host: true
  },
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'client/src'),
      '@assets': resolve(__dirname, 'client/assets'),
      '@shaders': resolve(__dirname, 'client/src/shaders'),
      '@utils': resolve(__dirname, 'client/src/utils'),
      '@systems': resolve(__dirname, 'client/src/systems'),
      '@components': resolve(__dirname, 'client/src/components')
    }
  },
  
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __PROD__: JSON.stringify(process.env.NODE_ENV === 'production'),
    __VERSION__: JSON.stringify(process.env.npm_package_version)
  },
  
  optimizeDeps: {
    include: [
      'three',
      'cannon-es',
      'socket.io-client'
    ],
    exclude: []
  },
  
  css: {
    devSourcemap: true
  }
});
