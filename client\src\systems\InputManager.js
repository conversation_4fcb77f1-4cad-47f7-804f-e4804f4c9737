/**
 * InputManager - <PERSON><PERSON> keyboard and mouse input for NEON SHARD
 *
 * Task 1.4: Implement keyboard and mouse input handling. Capture WASD for movement,
 * mouse for camera look, and basic actions (jump, fire).
 *
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

import { EventEmitter } from '../utils/EventEmitter.js';
import { Logger } from '../utils/Logger.js';

const logger = new Logger('InputManager');

/**
 * InputManager class for handling all user input
 */
export class InputManager extends EventEmitter {
  constructor(canvas) {
    super();

    this.canvas = canvas;
    this.isEnabled = false;
    this.isPointerLocked = false;

    // Keyboard state tracking
    this.keys = new Set();
    this.keyBindings = {
      // Movement
      'KeyW': 'forward',
      'KeyA': 'left',
      'KeyS': 'backward',
      'KeyD': 'right',

      // Actions
      'Space': 'jump',
      'ShiftLeft': 'sprint',
      'ShiftRight': 'sprint',
      'ControlLeft': 'crouch',
      'ControlRight': 'crouch',

      // Weapons
      'Digit1': 'weapon1',
      'Digit2': 'weapon2',
      'Digit3': 'weapon3',
      'Digit4': 'weapon4',
      'Digit5': 'weapon5',

      // Other
      'KeyR': 'reload',
      'Tab': 'scoreboard',
      'Escape': 'menu'
    };

    // Mouse state
    this.mouseState = {
      deltaX: 0,
      deltaY: 0,
      leftButton: false,
      rightButton: false,
      middleButton: false
    };

    // Mouse sensitivity
    this.mouseSensitivity = 0.002;

    // Bound event handlers (for proper removal)
    this.boundHandlers = {
      keyDown: this.handleKeyDown.bind(this),
      keyUp: this.handleKeyUp.bind(this),
      mouseDown: this.handleMouseDown.bind(this),
      mouseUp: this.handleMouseUp.bind(this),
      mouseMove: this.handleMouseMove.bind(this),
      contextMenu: this.handleContextMenu.bind(this),
      pointerLockChange: this.handlePointerLockChange.bind(this),
      pointerLockError: this.handlePointerLockError.bind(this)
    };
  }

  /**
   * Initialize the input manager
   */
  async init() {
    try {
      logger.info('Initializing InputManager...');

      if (!this.canvas) {
        throw new Error('Canvas element is required for InputManager');
      }

      this.setupEventListeners();
      this.isEnabled = true;

      logger.info('InputManager initialized successfully');

    } catch (error) {
      logger.error(`Failed to initialize InputManager: ${error.message}`);
      throw error;
    }
  }

  /**
   * Setup all event listeners
   */
  setupEventListeners() {
    // Keyboard events
    document.addEventListener('keydown', this.boundHandlers.keyDown);
    document.addEventListener('keyup', this.boundHandlers.keyUp);

    // Mouse events
    this.canvas.addEventListener('mousedown', this.boundHandlers.mouseDown);
    document.addEventListener('mouseup', this.boundHandlers.mouseUp);
    document.addEventListener('mousemove', this.boundHandlers.mouseMove);
    this.canvas.addEventListener('contextmenu', this.boundHandlers.contextMenu);

    // Pointer lock events
    document.addEventListener('pointerlockchange', this.boundHandlers.pointerLockChange);
    document.addEventListener('pointerlockerror', this.boundHandlers.pointerLockError);

    logger.debug('Event listeners setup complete');
  }

  /**
   * Handle keydown events
   */
  handleKeyDown(event) {
    if (!this.isEnabled) return;

    const code = event.code;
    const action = this.keyBindings[code];

    // Prevent default for game keys
    if (action) {
      event.preventDefault();
    }

    // Track key state
    if (!this.keys.has(code)) {
      this.keys.add(code);

      if (action) {
        logger.info(`Key pressed: ${action} (${code})`);
        this.emit('keyPressed', { action, code, key: event.key });
      } else {
        logger.debug(`Unmapped key pressed: ${code}`);
      }
    }
  }

  /**
   * Handle keyup events
   */
  handleKeyUp(event) {
    if (!this.isEnabled) return;

    const code = event.code;
    const action = this.keyBindings[code];

    // Remove from tracked keys
    this.keys.delete(code);

    if (action) {
      logger.info(`Key released: ${action} (${code})`);
      this.emit('keyReleased', { action, code, key: event.key });
    }
  }

  /**
   * Handle mouse down events
   */
  handleMouseDown(event) {
    if (!this.isEnabled) return;

    event.preventDefault();

    // Update mouse state
    switch (event.button) {
      case 0: // Left button
        this.mouseState.leftButton = true;
        logger.info('Mouse button pressed: LEFT (fire)');
        this.emit('mousePressed', { button: 'left', action: 'fire' });
        break;
      case 1: // Middle button
        this.mouseState.middleButton = true;
        logger.info('Mouse button pressed: MIDDLE');
        this.emit('mousePressed', { button: 'middle', action: 'middle' });
        break;
      case 2: // Right button
        this.mouseState.rightButton = true;
        logger.info('Mouse button pressed: RIGHT (aim)');
        this.emit('mousePressed', { button: 'right', action: 'aim' });
        break;
    }

    // Request pointer lock on left click
    if (event.button === 0 && !this.isPointerLocked) {
      this.requestPointerLock();
    }
  }

  /**
   * Handle mouse up events
   */
  handleMouseUp(event) {
    if (!this.isEnabled) return;

    // Update mouse state
    switch (event.button) {
      case 0: // Left button
        this.mouseState.leftButton = false;
        logger.info('Mouse button released: LEFT');
        this.emit('mouseReleased', { button: 'left', action: 'fire' });
        break;
      case 1: // Middle button
        this.mouseState.middleButton = false;
        logger.info('Mouse button released: MIDDLE');
        this.emit('mouseReleased', { button: 'middle', action: 'middle' });
        break;
      case 2: // Right button
        this.mouseState.rightButton = false;
        logger.info('Mouse button released: RIGHT');
        this.emit('mouseReleased', { button: 'right', action: 'aim' });
        break;
    }
  }

  /**
   * Handle mouse move events
   */
  handleMouseMove(event) {
    if (!this.isEnabled) return;

    if (this.isPointerLocked) {
      // Use movement deltas when pointer is locked
      const deltaX = event.movementX * this.mouseSensitivity;
      const deltaY = event.movementY * this.mouseSensitivity;

      this.mouseState.deltaX = deltaX;
      this.mouseState.deltaY = deltaY;

      logger.debug(`Mouse moved: deltaX=${deltaX.toFixed(4)}, deltaY=${deltaY.toFixed(4)}`);
      this.emit('mouseMove', { deltaX, deltaY, locked: true });
    } else {
      // Regular mouse movement when not locked
      logger.debug(`Mouse moved: x=${event.clientX}, y=${event.clientY}`);
      this.emit('mouseMove', { x: event.clientX, y: event.clientY, locked: false });
    }
  }

  /**
   * Prevent context menu on right click
   */
  handleContextMenu(event) {
    event.preventDefault();
  }

  /**
   * Handle pointer lock change
   */
  handlePointerLockChange() {
    this.isPointerLocked = document.pointerLockElement === this.canvas;

    if (this.isPointerLocked) {
      logger.info('Pointer lock activated - FPS mouse look enabled');
      this.emit('pointerLockChanged', { locked: true });
    } else {
      logger.info('Pointer lock deactivated');
      this.emit('pointerLockChanged', { locked: false });
    }
  }

  /**
   * Handle pointer lock error
   */
  handlePointerLockError() {
    logger.error('Pointer lock failed');
    this.emit('pointerLockError');
  }

  /**
   * Request pointer lock for FPS-style mouse control
   */
  requestPointerLock() {
    if (this.canvas.requestPointerLock) {
      this.canvas.requestPointerLock();
      logger.debug('Pointer lock requested');
    } else {
      logger.warn('Pointer lock not supported');
    }
  }

  /**
   * Exit pointer lock
   */
  exitPointerLock() {
    if (document.exitPointerLock) {
      document.exitPointerLock();
      logger.debug('Pointer lock exit requested');
    }
  }

  /**
   * Check if a key is currently pressed
   */
  isKeyPressed(code) {
    return this.keys.has(code);
  }

  /**
   * Check if an action is currently active
   */
  isActionActive(action) {
    for (const [code, mappedAction] of Object.entries(this.keyBindings)) {
      if (mappedAction === action && this.keys.has(code)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Get current mouse state
   */
  getMouseState() {
    return { ...this.mouseState };
  }

  /**
   * Update method - called each frame
   */
  update(deltaTime) {
    // Reset mouse deltas after each frame
    this.mouseState.deltaX = 0;
    this.mouseState.deltaY = 0;
  }

  /**
   * Enable input handling
   */
  enable() {
    this.isEnabled = true;
    logger.debug('InputManager enabled');
  }

  /**
   * Disable input handling
   */
  disable() {
    this.isEnabled = false;
    logger.debug('InputManager disabled');
  }

  /**
   * Set pointer locked state (for external management)
   */
  setPointerLocked(locked) {
    this.isPointerLocked = locked;
  }

  /**
   * Cleanup and remove event listeners
   */
  shutdown() {
    logger.info('Shutting down InputManager...');

    // Remove all event listeners
    document.removeEventListener('keydown', this.boundHandlers.keyDown);
    document.removeEventListener('keyup', this.boundHandlers.keyUp);
    this.canvas.removeEventListener('mousedown', this.boundHandlers.mouseDown);
    document.removeEventListener('mouseup', this.boundHandlers.mouseUp);
    document.removeEventListener('mousemove', this.boundHandlers.mouseMove);
    this.canvas.removeEventListener('contextmenu', this.boundHandlers.contextMenu);
    document.removeEventListener('pointerlockchange', this.boundHandlers.pointerLockChange);
    document.removeEventListener('pointerlockerror', this.boundHandlers.pointerLockError);

    // Exit pointer lock if active
    if (this.isPointerLocked) {
      this.exitPointerLock();
    }

    // Clear state
    this.keys.clear();
    this.isEnabled = false;

    logger.info('InputManager shutdown complete');
  }
}
