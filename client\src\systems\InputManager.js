/**
 * InputManager - <PERSON><PERSON> keyboard and mouse input for NEON SHARD
 *
 * Task 1.4: Implement keyboard and mouse input handling. Capture WASD for movement,
 * mouse for camera look, and basic actions (jump, fire).
 *
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

import { EventEmitter } from '../utils/EventEmitter.js';
import { Logger } from '../utils/Logger.js';

const logger = new Logger('InputManager');

/**
 * InputManager class for handling all user input
 */
export class InputManager extends EventEmitter {
  constructor(canvas) {
    super();

    this.canvas = canvas;
    this.isEnabled = false;
    this.isPointerLocked = false;

    // Keyboard state tracking
    this.keys = new Set();
    this.keyBindings = {
      // Movement
      'KeyW': 'forward',
      'KeyA': 'left',
      'KeyS': 'backward',
      'KeyD': 'right',

      // Actions
      'Space': 'jump',
      'ShiftLeft': 'sprint',
      'ShiftRight': 'sprint',
      'ControlLeft': 'crouch',
      'ControlRight': 'crouch',

      // Weapons
      'Digit1': 'weapon1',
      'Digit2': 'weapon2',
      'Digit3': 'weapon3',
      'Digit4': 'weapon4',
      'Digit5': 'weapon5',

      // Other
      'KeyR': 'reload',
      'Tab': 'scoreboard',
      'Escape': 'menu'
    };

    // Mouse state
    this.mouseState = {
      deltaX: 0,
      deltaY: 0,
      leftButton: false,
      rightButton: false,
      middleButton: false
    };

    // Mouse sensitivity
    this.mouseSensitivity = 0.002;

    // Bound event handlers (for proper removal)
    this.boundHandlers = {
      keyDown: this.handleKeyDown.bind(this),
      keyUp: this.handleKeyUp.bind(this),
      mouseDown: this.handleMouseDown.bind(this),
      mouseUp: this.handleMouseUp.bind(this),
      mouseMove: this.handleMouseMove.bind(this),
      contextMenu: this.handleContextMenu.bind(this),
      pointerLockChange: this.handlePointerLockChange.bind(this),
      pointerLockError: this.handlePointerLockError.bind(this)
    };
  }
