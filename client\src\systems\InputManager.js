/**
 * InputManager - <PERSON><PERSON> keyboard and mouse input
 * 
 * <AUTHOR> Shard Development Team
 * @version 1.0.0
 */

import { EventEmitter } from '../utils/EventEmitter.js';
import { Logger } from '../utils/Logger.js';

const logger = new Logger('InputManager');

export class InputManager extends EventEmitter {
  constructor(canvas) {
    super();
    this.canvas = canvas;
    this.keys = new Set();
    this.isPointerLocked = false;
  }

  async init() {
    logger.info('Initializing InputManager...');
    this.setupEventListeners();
    logger.info('InputManager initialized');
  }

  setupEventListeners() {
    // TODO: Setup keyboard and mouse event listeners
  }

  update(deltaTime) {
    // TODO: Process input state
  }

  setPointerLocked(locked) {
    this.isPointerLocked = locked;
  }

  shutdown() {
    // TODO: Remove event listeners
  }
}
